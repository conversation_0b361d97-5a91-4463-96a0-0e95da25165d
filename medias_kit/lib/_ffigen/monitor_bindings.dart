// 功能: 监视器FFI函数绑定

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
@ffi.DefaultAsset('package:medias_kit/_ffigen/monitor_bindings.dart')
library;

import 'dart:ffi' as ffi;

/// 创建监视器
/// 返回监视器引用
@ffi.Native<MonitorRef Function()>()
external MonitorRef monitorCreate();

/// 释放监视器
/// - [monitor] 监视器引用
@ffi.Native<ffi.Void Function(MonitorRef)>()
external void monitorDestroy(
  MonitorRef monitor,
);

/// 绑定视频捕获和纹理渲染器
/// - [monitor] 监视器引用
/// - [capture] 视频捕获引用
/// - [renderer] 纹理渲染器引用
/// - [autoStart] 是否自动开始
@ffi.Native<
    ffi.Void Function(
        MonitorRef, ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>, ffi.Bool)>()
external void monitorBind(
  MonitorRef monitor,
  ffi.Pointer<ffi.Void> capture,
  ffi.Pointer<ffi.Void> renderer,
  bool autoStart,
);

/// 开始监视
/// - [monitor] 监视器引用
@ffi.Native<ffi.Void Function(MonitorRef)>()
external void monitorStart(
  MonitorRef monitor,
);

/// 停止监视
/// - [monitor] 监视器引用
@ffi.Native<ffi.Void Function(MonitorRef)>()
external void monitorStop(
  MonitorRef monitor,
);

/// 监视器引用类型
typedef MonitorRef = ffi.Pointer<ffi.Void>;
