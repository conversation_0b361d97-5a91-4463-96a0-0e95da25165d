// 功能: 共享内存FFI函数绑定

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
@ffi.DefaultAsset('package:medias_kit/_ffigen/shared_memory_bindings.dart')
library;

import 'dart:ffi' as ffi;

/// 打开共享内存
/// - [key] 共享内存的唯一标识符
/// - [size] 共享内存的大小
/// 返回共享内存引用
@ffi.Native<SharedMemoryRef Function(ffi.Pointer<ffi.Char>, ffi.Size)>()
external SharedMemoryRef sharedMemoryOpen(
  ffi.Pointer<ffi.Char> key,
  int size,
);

/// 释放共享内存
/// - [handle] 共享内存段的句柄
@ffi.Native<ffi.Void Function(ffi.Pointer<ffi.Char>)>()
external void sharedMemoryFree(
  ffi.Pointer<ffi.Char> key,
);

/// 读取共享内存, 返回值表示是否成功
/// - [memory] 共享内存引用
/// - [buffer] 读取数据的缓冲区
/// - [size] 要读取的数据大小
@ffi.Native<
    ffi.Bool Function(SharedMemoryRef, ffi.Pointer<ffi.Void>, ffi.Size)>()
external bool sharedMemoryRead(
  SharedMemoryRef memory,
  ffi.Pointer<ffi.Void> buffer,
  int size,
);

/// 写入共享内存, 返回值表示是否成功
/// - [memory] 共享内存引用
/// - [data] 要写入的数据
/// - [size] 要写入的数据大小
@ffi.Native<
    ffi.Bool Function(SharedMemoryRef, ffi.Pointer<ffi.Void>, ffi.Size)>()
external bool sharedMemoryWrite(
  SharedMemoryRef memory,
  ffi.Pointer<ffi.Void> data,
  int size,
);

/// 共享内存引用类型
typedef SharedMemoryRef = ffi.Pointer<ffi.Void>;
typedef OnEventCallbackFunction = ffi.Void Function(ffi.Int code);
typedef DartOnEventCallbackFunction = void Function(int code);

/// 事件回调函数类型
typedef OnEventCallback
    = ffi.Pointer<ffi.NativeFunction<OnEventCallbackFunction>>;
