// 功能: 向量FFI函数绑定
//
// 该文件定义了向量操作的FFI绑定，用于在Dart和C++之间传递向量数据

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
@ffi.DefaultAsset('package:medias_kit/_ffigen/vector_bindings.dart')
library;

import 'dart:ffi' as ffi;

/// 创建向量
/// 返回向量引用
@ffi.Native<VectorRef Function()>()
external VectorRef vectorCreate();

/// 销毁向量
/// - [vector] 向量引用
/// - [freeElements] 是否释放向量元素
@ffi.Native<ffi.Void Function(VectorRef, ffi.Bool)>()
external void vectorDestroy(
  VectorRef vector,
  bool freeElements,
);

/// 获取向量元素数量
/// - [vector] 向量引用
@ffi.Native<ffi.Int Function(VectorRef)>()
external int vectorSize(
  VectorRef vector,
);

/// 获取向量元素
/// - [vector] 向量引用
/// - [index] 元素索引
@ffi.Native<ffi.Pointer<ffi.Void> Function(VectorRef, ffi.Int)>()
external ffi.Pointer<ffi.Void> vectorElementAt(
  VectorRef vector,
  int index,
);

/// 向量引用类型
typedef VectorRef = ffi.Pointer<ffi.Void>;
