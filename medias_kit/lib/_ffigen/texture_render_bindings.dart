// 功能: 纹理渲染FFI函数绑定

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
@ffi.DefaultAsset('package:medias_kit/_ffigen/texture_render_bindings.dart')
library;

import 'dart:ffi' as ffi;

/// 创建纹理渲染器
/// - [pluginId] 插件ID，用于获取对应的 MediasKitPlugin 实例, 取值来源 [MediasKit.pluginId]
/// - [width] 纹理宽度
/// - [height] 纹理高度
/// 返回纹理渲染器引用
@ffi.Native<
    TextureRendererRef Function(ffi.Pointer<ffi.Char>, ffi.Int, ffi.Int)>()
external TextureRendererRef textureRendererCreate(
  ffi.Pointer<ffi.Char> pluginId,
  int width,
  int height,
);

/// 释放纹理渲染器
/// - [renderer] 纹理渲染器引用
@ffi.Native<ffi.Void Function(TextureRendererRef)>()
external void textureRendererDestroy(
  TextureRendererRef renderer,
);

/// 渲染一帧数据
/// - [renderer] 纹理渲染器引用
/// - [frame] 帧数据
/// - [frameLen] 帧数据长度
@ffi.Native<
    ffi.Void Function(TextureRendererRef, ffi.Pointer<ffi.Uint8>, ffi.Long)>()
external void textureRendererRenderFrame(
  TextureRendererRef renderer,
  ffi.Pointer<ffi.Uint8> frame,
  int frameLen,
);

/// 获取纹理ID
/// - [renderer] 纹理渲染器引用
/// 返回纹理ID，可用于Flutter的Texture组件
@ffi.Native<ffi.Int64 Function(TextureRendererRef)>()
external int textureRendererGetTextureId(
  TextureRendererRef renderer,
);

/// 纹理渲染器引用类型
typedef TextureRendererRef = ffi.Pointer<ffi.Void>;
