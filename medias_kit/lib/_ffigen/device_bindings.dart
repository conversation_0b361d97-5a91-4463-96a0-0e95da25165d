// 功能: 设备FFI函数绑定

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
@ffi.DefaultAsset('package:medias_kit/_ffigen/device_bindings.dart')
library;

import 'dart:ffi' as ffi;

/// 获取主机运行环境平台信息
/// 返回值为静态变量不需要 [malloc.free]
@ffi.Native<ffi.Pointer<ffi.Char> Function()>()
external ffi.Pointer<ffi.Char> getHostDevicePlatform();

/// 获取主机指定磁盘空间信息
/// - [path] 磁盘路径
/// 返回值需要 [malloc.free]
@ffi.Native<HostDeviceSpaceInfoRef Function(ffi.Pointer<ffi.Char>)>()
external HostDeviceSpaceInfoRef getHostDeviceSpaceInfo(
  ffi.Pointer<ffi.Char> path,
);

/// 获取所有视频采集设备
/// 返回向量引用, 需要手动释, 元素类型为 <char *>
@ffi.Native<VectorRef Function()>()
external VectorRef getVideoCaptureDevices();

/// 添加视频设备监听端口
/// - [port] 监听端口
@ffi.Native<ffi.Void Function(Dart_Port_DL)>()
external void addMonitorPortForVideoDevices(
  int port,
);

@ffi.Native<ffi.Void Function(Dart_Port_DL)>()
external void addMonitorPortForAudioDevices(
  int port,
);

@ffi.Native<ffi.Void Function(ffi.Pointer<ffi.Char>, ffi.Int)>()
external void setAudioSourceVolume(
  ffi.Pointer<ffi.Char> name,
  int volume,
);

@ffi.Native<ffi.Void Function(ffi.Pointer<ffi.Char>, ffi.Int)>()
external void setAudioSinkVolume(
  ffi.Pointer<ffi.Char> name,
  int volume,
);

@ffi.Native<ffi.Void Function(ffi.Bool)>()
external void setDeviceIsOnline(
  bool isOnline,
);

@ffi.Native<ffi.Bool Function()>()
external bool getDeviceIsOnline();

typedef pa_volume_t = ffi.Uint32;
typedef Dartpa_volume_t = int;

final class pa_cvolume extends ffi.Struct {
  @ffi.Uint8()
  external int channels;

  @ffi.Array.multi([32])
  external ffi.Array<pa_volume_t> values;
}

/// 音频设备
final class AudioDevice extends ffi.Struct {
  /// 是否为默认设备
  @ffi.Bool()
  external bool isDefault;

  /// 设备索引
  @ffi.Uint32()
  external int index;

  /// 设备名称
  external ffi.Pointer<ffi.Char> name;

  /// 设备描述
  external ffi.Pointer<ffi.Char> description;

  /// 设备音量
  external pa_cvolume volume;

  /// 设备静音
  @ffi.Int()
  external int mute;
}

/// 视频采集设备
final class VideoCaptureDevice extends ffi.Struct {
  /// 设备索引
  @ffi.Int()
  external int index;

  /// 设备路径
  external ffi.Pointer<ffi.Char> path;

  /// 设备名称
  external ffi.Pointer<ffi.Char> name;

  /// true 为 USB 扩展, false 为内置PCIE设备
  @ffi.Bool()
  external bool isUsbExtend;
}

/// 主机设备磁盘空间信息
final class HostDeviceSpaceInfo extends ffi.Struct {
  /// 总容量
  @ffi.Uint64()
  external int capacity;

  /// 总剩余空间
  @ffi.Uint64()
  external int free;

  /// 当前用户配额下剩余空间
  @ffi.Uint64()
  external int available;
}

/// 主机设备磁盘空间信息引用类型
typedef HostDeviceSpaceInfoRef = ffi.Pointer<HostDeviceSpaceInfo>;

/// 向量引用类型
typedef VectorRef = ffi.Pointer<ffi.Void>;
typedef Dart_Port_DL = ffi.Int64;
typedef DartDart_Port_DL = int;
