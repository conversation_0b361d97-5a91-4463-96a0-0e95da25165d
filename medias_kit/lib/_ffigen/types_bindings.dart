// 功能: 类型FFI映射绑定
//
// 该文件定义了 dart 与 C 交互的基本类型和枚举

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
/// 像素格式枚举, 定义了视频帧的像素格式类型
///
/// - [pf_nv12]: YUV 4:2:0 格式，Y 平面后跟交错的 UV 平面
/// - [pf_i420]: YUV 4:2:0 格式，又称 YU12，三个独立平面 Y、U、V
/// - [pf_rgba]: RGB 格式，每像素 4 字节，包含透明通道
enum PixelFormat {
  /// NV12 格式 (YUV 4:2:0，Y 平面后跟交错的 UV 平面)
  pf_nv12(0),

  /// I420 格式 (YUV 4:2:0，三个独立平面 Y、U、V)
  pf_i420(1),

  /// RGBA 格式 (每像素 4 字节，包含透明通道)
  pf_rgba(2);

  final int value;
  const PixelFormat(this.value);

  static PixelFormat fromValue(int value) => switch (value) {
        0 => pf_nv12,
        1 => pf_i420,
        2 => pf_rgba,
        _ => throw ArgumentError('Unknown value for PixelFormat: $value'),
      };
}

/// 核心事件枚举, 定义了核心模块的事件类型
enum CoreEvent {
  /// 绑定原生插件
  ce_bindNativePlugin(0);

  final int value;
  const CoreEvent(this.value);

  static CoreEvent fromValue(int value) => switch (value) {
        0 => ce_bindNativePlugin,
        _ => throw ArgumentError('Unknown value for CoreEvent: $value'),
      };
}
