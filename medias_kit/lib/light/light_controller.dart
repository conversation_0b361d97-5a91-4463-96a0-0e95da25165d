import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart';

/// LED灯行为类型
enum LightAction {
  /// 关闭
  off("5A A5 04 04 00 00 00 00 00 00 00 00 A5 5A"),

  /// 蓝灯
  blue("5A A5 04 04 00 00 00 00 01 01 00 00 A5 5A"),

  /// 蓝灯闪烁
  blueFlashing("5A A5 04 04 00 00 00 00 03 30 00 00 A5 5A"),

  /// 绿灯
  green("5A A5 04 04 00 00 01 01 00 00 00 00 A5 5A"),

  /// 绿灯闪烁
  greenFlashing("5A A5 04 04 00 00 03 30 00 00 00 00 A5 5A"),

  /// 红灯
  red("5A A5 04 04 01 01 00 00 00 00 00 00 A5 5A"),

  /// 红灯闪烁
  redFlashing("5A A5 04 04 03 30 00 00 00 00 00 00 A5 5A"),

  /// 黄灯
  yellow("5A A5 04 04 01 01 01 01 00 00 00 00 A5 5A");

  final String _value;

  const LightAction(this._value);

  Uint8List get _cmd {
    final hex = _value.replaceAll(' ', '');
    List<int> bytes = [];
    for (int i = 0; i < hex.length; i += 2) {
      bytes.add(int.parse(hex.substring(i, i + 2), radix: 16));
    }
    return Uint8List.fromList(bytes);
  }
}

/// 呼吸灯控制器
class LightController {
  LightController._();

  static final share = LightController._();

  final port = SerialPort("/dev/ttyCH341USB0");

  void active(LightAction action) {
    if (!port.isOpen) {
      final isOpen = port.openReadWrite();
      if (!isOpen) {
        throw Exception("串口打开失败 >>> ${SerialPort.lastError}");
      }
      final config = SerialPortConfig();
      config.baudRate = 115200;
      config.bits = 8;
      config.parity = 0;
      config.stopBits = 1;
      config.setFlowControl(SerialPortFlowControl.none);
      try {
        port.config = config;
      } catch (e) {
        rethrow;
      }
    }
    port.write(action._cmd);
  }
}
