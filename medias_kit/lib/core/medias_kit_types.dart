/// 磁盘空间信息
class SpaceInfo {
  /// 总容量
  final int capacity;

  /// 总剩余空间
  final int free;

  /// 当前用户配额下剩余空间
  final int available;

  SpaceInfo({
    required this.capacity,
    required this.free,
    required this.available,
  });

  Map<String, dynamic> toMap() {
    return {
      'capacity': capacity,
      'free': free,
      'available': available,
    };
  }

  @override
  String toString() => toMap().toString();
}

/// 流类型
enum StreamType {
  /// 主流
  main,

  /// 副流
  secondary,
}
