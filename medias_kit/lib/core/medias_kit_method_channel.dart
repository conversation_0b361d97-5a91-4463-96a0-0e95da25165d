import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'medias_kit_module.dart';
import 'medias_kit_platform_interface.dart';
import '../_ffigen/types_bindings.dart';

/// An implementation of [MediasKitPlatform] that uses method channels.
class MethodChannelMediasKit extends MediasKitPlatform {
  MethodChannelMediasKit() {
    methodChannel.setMethodCallHandler((call) async {
      final params = call.method.split(".");
      final module = params.first;
      final eventID = int.parse(params.last);
      debugPrint("$module.$eventID");
      switch (module) {
        default:
          throw MissingPluginException("Not implementation!");
      }
    });
  }

  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('medias_kit');

  //********************************** Module Core **********************************/
  @override
  Future<String?> bindNativePlugin() async {
    // TODO: 待移除
    // final pathSegments = Platform.resolvedExecutable.split("/")..removeLast();
    return methodChannel.invokeMethod<String>(
      Module.core.method(CoreEvent.ce_bindNativePlugin.index),
    );
  }
}
