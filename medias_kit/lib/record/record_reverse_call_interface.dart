import 'package:flutter/foundation.dart';

/// 视频信息
class VideoInfo {
  /// 名称
  final String name;

  /// 存储路径, 为空代表失败
  final String? path;

  /// 首个 I 帧, 偏移量
  final int pointOffset;

  /// 总帧数
  final int frameCount;

  /// 构造函数
  VideoInfo({
    required this.name,
    required this.path,
    required this.pointOffset,
    required this.frameCount,
  });

  @override
  String toString() {
    return "${{
      "name": name,
      "path": path,
      "pointOffset": pointOffset,
      "frameCount": frameCount,
    }}";
  }
}

/// 录制反向调用接口
abstract class RecordReverseCallInterface {
  /// 视频合成
  final void Function(VideoInfo videoInfo)? onMerged;

  /// 是否有画面采集信号
  final hasSignal = ValueNotifier(false);

  /// 构造函数
  ///
  /// - [onMerged] 默认值: 缺省
  RecordReverseCallInterface({
    this.onMerged,
  });
}
