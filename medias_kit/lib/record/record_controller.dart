import 'package:flutter/foundation.dart';
import 'package:medias_kit/record/record_reverse_call_interface.dart';

import '../core/medias_kit_platform_interface.dart';
import '../device/host_device.dart';

/// 录制控制器
class RecordController extends RecordReverseCallInterface {
  /// 录制控制器标志符
  final String identifier = 'RecordController.${UniqueKey()}';

  /// 关联名称, 文件域名称
  final String spaceName;

  /// 视频采集设备的名称, 缺省: 为内置 PCI 接口设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final VideoDevice? videoDevice;

  /// 音频输入源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSources] 中获取
  String get audioSourceName => _audioSourceName;
  String _audioSourceName;

  /// 采集宽度
  final int width;

  /// 采集高度
  final int height;

  /// 采集帧率
  final int framerate;

  /// 视频编码码率
  final int videoBitrate;

  /// 音频采样率
  final int sampleRate;

  /// 音频编码码率
  final int audioBitrate;

  /// 音频通道数
  final int numChannels;

  /// 是否初始化
  bool _isInit = false;

  /// 是否录制音频
  bool _enableAudioRecord = false;

  /// 构造函数
  ///
  /// - [spaceName] 必传, 业务上为手术ID
  ///
  /// - [videoDevice] 默认值: 缺省
  ///
  /// - [audioSourceName] 默认值: default
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 30
  ///
  /// - [videoBitrate] 默认值: 4096kbps
  ///
  /// - [sampleRate] 默认值: 48000Hz
  ///
  /// - [audioBitrate] 默认值: 96000bps
  ///
  /// - [numChannels] 默认值: 1
  RecordController({
    required this.spaceName,
    this.videoDevice,
    String audioSourceName = "default",
    this.width = 1920,
    this.height = 1080,
    this.framerate = 30,
    this.videoBitrate = 4096,
    this.sampleRate = 48000,
    this.audioBitrate = 96000,
    this.numChannels = 1,
    super.onMerged,
  }) : _audioSourceName = audioSourceName;

  /// 初始化资源
  Future<bool> init() async {
    if (_isInit) return true;
    _isInit = await MediasKitPlatform.instance.recordControllerInit(
          this,
          identifier,
          spaceName,
          videoDevice,
          audioSourceName,
          width,
          height,
          framerate,
          videoBitrate,
          sampleRate,
          audioBitrate,
          numChannels,
        ) ??
        false;
    return _isInit;
  }

  /// 开始
  Future<void> start() async {
    if (_isInit) {
      return MediasKitPlatform.instance.recordControllerStart(identifier);
    }
  }

  /// 停止
  Future<void> stop() async {
    if (_isInit) {
      return MediasKitPlatform.instance.recordControllerStop(identifier);
    }
  }

  /// 获取是否允许录制音频
  bool get enableAudioRecord => _enableAudioRecord;

  Future<void> recordAudio(bool enable) async {
    if (_isInit) {
      _enableAudioRecord = enable;
      return MediasKitPlatform.instance
          .recordControllerRecordAudio(identifier, enable);
    }
  }

  /// 切换音频输入源
  ///
  /// - [name] 输出源名称, 缺省: default
  Future<void> changeAudioSource({String name = "default"}) async {
    if (_isInit) {
      _audioSourceName = name;
      return MediasKitPlatform.instance
          .recordControllerChangeAudioSource(identifier, name);
    }
  }

  /// 视频合并, 返回合并后的文件路径
  ///
  /// - [name] 文件名
  ///
  /// - [beginTime] 开始时间戳
  ///
  /// - [endTime] 结束时间戳
  ///
  /// - [needEndPrecision] 是否需要精确结束时间帧, false: 结束帧会自动调整为关键帧前一帧
  Future<void> merge({
    required String name,
    required int beginTime,
    required int endTime,
    required bool needEndPrecision,
  }) async {
    if (_isInit) {
      return MediasKitPlatform.instance.recordControllerMerge(
        identifier,
        name,
        beginTime,
        endTime,
        needEndPrecision,
      );
    }
  }

  /// 当前录制视频帧总数
  Future<int> frameCount() async {
    if (_isInit) {
      return MediasKitPlatform.instance.recordControllerFrameCount(identifier);
    }
    return 0;
  }

  /// 当前录制视频的首帧时间戳+错误偏移
  Future<int> firstFrameTimestampWithOffset() async {
    if (_isInit) {
      return MediasKitPlatform.instance
          .recordControllerFirstFrameTimestampWithOffset(identifier);
    }
    return 0;
  }

  /// 释放资源, 标志结束录制
  Future<void> dispose() async {
    _isInit = false;
    return MediasKitPlatform.instance.recordControllerDispose(identifier);
  }
}
