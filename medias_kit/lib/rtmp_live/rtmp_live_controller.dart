import 'package:flutter/foundation.dart';

import '../core/medias_kit_platform_interface.dart';
import '../core/medias_kit_types.dart';
import '../device/host_device.dart';

/// CDN直播控制器
class RtmpLiveController {
  /// CDN直播控制器标志符
  final String identifier = 'RtmpLiveController.${UniqueKey()}';

  /// 流类型
  final StreamType streamType;

  /// 声网应用 ID
  final String appId;

  /// CDN推流地址
  final String url;

  /// 视频采集设备的名称, 缺省: 为内置 PCI 接口设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final VideoDevice? videoDevice;

  /// 音频输入源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSources] 中获取
  String get audioSourceName => _audioSourceName;
  String _audioSourceName;

  /// 视频采集宽度
  final int width;

  /// 视频采集高度
  final int height;

  /// 视频采集帧率
  final int framerate;

  /// 视频编码码率
  final int bitrate;

  /// 音频采样率
  final int sampleRate;

  /// 音频通道数量
  final int numChannels;

  /// 是否初始化
  bool _isInit = false;

  /// 是否允许说话
  bool _enableSpeak = false;

  /// 是否推送rtc音频
  final bool isCommitRtcAudio;

  /// 构造函数
  ///
  /// - [videoDevice] 默认值: 缺省
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 30
  ///
  /// - [bitrate] 默认值: 4096kbps
  ///
  /// - [sampleRate] 默认值: 48000
  ///
  /// - [numChannels] 默认值: 1
  RtmpLiveController({
    required this.streamType,
    required this.appId,
    required this.url,
    this.videoDevice,
    String audioSourceName = 'default',
    this.width = 1920,
    this.height = 1080,
    this.framerate = 30,
    this.bitrate = 4096,
    this.sampleRate = 48000,
    this.numChannels = 1,
    this.isCommitRtcAudio = false,
  }) : _audioSourceName = audioSourceName;

  /// 初始化资源
  Future<bool> init() async {
    if (_isInit) return true;
    _isInit = await MediasKitPlatform.instance.rtmpLiveControllerInit(
          identifier,
          streamType,
          appId,
          url,
          videoDevice,
          audioSourceName,
          width,
          height,
          framerate,
          bitrate,
          sampleRate,
          numChannels,
          isCommitRtcAudio,
        ) ??
        false;
    return _isInit;
  }

  /// 开始
  Future<void> start() async {
    if (_isInit) {
      return MediasKitPlatform.instance.rtmpLiveControllerStart(identifier);
    }
  }

  /// 停止
  Future<void> stop() async {
    if (_isInit) {
      return MediasKitPlatform.instance.rtmpLiveControllerStop(identifier);
    }
  }

  /// 获取是否允许说话状态
  bool get enableSpeak => _enableSpeak;

  /// 设置是否允许说话
  ///
  /// - [enable]
  Future<void> speak(bool enable) async {
    if (_isInit) {
      _enableSpeak = enable;
      return MediasKitPlatform.instance
          .rtmpLiveControllerSpeak(identifier, enable);
    }
  }

  /// 切换音频输入源
  ///
  /// - [name] 输出源名称, 缺省: default
  Future<void> changeAudioSource({String name = "default"}) async {
    if (_isInit) {
      _audioSourceName = name;
      return MediasKitPlatform.instance
          .rtmpLiveControllerChangeAudioSink(identifier, name);
    }
  }

  /// 释放资源, 标志结束直播
  Future<void> dispose() async {
    _isInit = false;
    _enableSpeak = false;
    return MediasKitPlatform.instance.rtmpLiveControllerDispose(identifier);
  }
}
