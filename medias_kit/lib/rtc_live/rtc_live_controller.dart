import 'package:flutter/foundation.dart';

import '../core/medias_kit_types.dart';
import '../core/medias_kit_platform_interface.dart';
import '../device/host_device.dart';
import 'rtc_live_reverse_call_interface.dart';

/// 会诊直播控制器
class RtcLiveController extends RtcLiveReverseCallInterface {
  /// 会诊直播控制器标志符
  final String identifier = 'RtcLiveController.${UniqueKey()}';

  /// 流类型
  final StreamType streamType;

  /// 声网应用 ID
  final String appId;

  /// 声网鉴权 token
  final String token;

  /// 声网频道 ID
  final String channelId;

  /// 声网用户 ID
  final String userId;

  /// 录制控制器标志符
  final String? recordControllerIdentifier;

  /// 视频采集设备的名称, 缺省: 为内置 PCI 接口设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final VideoDevice? videoDevice;

  /// 音频输入源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSources] 中获取
  String get audioSourceName => _audioSourceName;
  String _audioSourceName;

  /// 音频输出源名称, 缺省为 default
  ///
  /// - [HostDevice.audioSinks] 中获取
  String get audioSinkName => _audioSinkName;
  String _audioSinkName;

  /// 音频输出音量
  double get audioSinkVolume => _audioSinkVolume;
  double _audioSinkVolume;

  /// 视频采集宽度
  final int width;

  /// 视频采集高度
  final int height;

  /// 视频采集帧率
  final int framerate;

  /// 视频编码码率
  final int bitrate;

  /// 音频采样率
  final int sampleRate;

  /// 音频通道数量
  final int numChannels;

  /// 是否初始化
  bool _isInit = false;

  /// 是否允许说话
  bool _enableSpeak = false;

  /// 构造函数
  ///
  /// - [recordControllerIdentifier] 录制控制器标志符: 缺省
  ///
  /// - [videoDevice] 默认值: 缺省
  ///
  /// - [audioSourceName] 默认值: default
  ///
  /// - [audioSinkName] 默认值: default
  ///
  /// - [audioSinkVolume] 默认值: 1.0
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 30
  ///
  /// - [bitrate] 默认值: 0kbps
  ///
  /// - [sampleRate] 默认值: 48000
  ///
  /// - [numChannels] 默认值: 1
  RtcLiveController({
    required this.streamType,
    required this.appId,
    required this.token,
    required this.channelId,
    required this.userId,
    this.recordControllerIdentifier,
    this.videoDevice,
    String audioSourceName = "default",
    String audioSinkName = "default",
    double audioSinkVolume = 1.0,
    this.width = 1920,
    this.height = 1080,
    this.framerate = 30,
    this.bitrate = 0,
    this.sampleRate = 48000,
    this.numChannels = 1,
    super.onUserJoined,
    super.onUserLeft,
    super.onTransportStats,
    super.onNetworkQuality,
    super.onNetworkType,
    super.onReInit,
  })  : _audioSourceName = audioSourceName,
        _audioSinkName = audioSinkName,
        _audioSinkVolume = audioSinkVolume;

  /// 初始化资源
  Future<bool> init() async {
    if (_isInit) return true;
    _isInit = await MediasKitPlatform.instance.rtcLiveControllerInit(
          this,
          identifier,
          streamType,
          appId,
          token,
          channelId,
          userId,
          recordControllerIdentifier,
          videoDevice,
          audioSourceName,
          audioSinkName,
          audioSinkVolume * 1.8,
          width,
          height,
          framerate,
          bitrate,
          sampleRate,
          numChannels,
        ) ??
        false;
    return _isInit;
  }

  /// 开始
  Future<void> start() async {
    if (_isInit) {
      return MediasKitPlatform.instance.rtcLiveControllerStart(identifier);
    }
  }

  /// 停止
  Future<void> stop() async {
    if (_isInit) {
      return MediasKitPlatform.instance.rtcLiveControllerStop(identifier);
    }
  }

  /// 获取是否允许说话状态
  bool get enableSpeak => _enableSpeak;

  /// 设置是否允许说话
  ///
  /// - [enable]
  Future<void> speak(bool enable) async {
    if (_isInit) {
      _enableSpeak = enable;
      return MediasKitPlatform.instance
          .rtcLiveControllerSpeak(identifier, enable);
    }
  }

  /// 切换音频输入源
  ///
  /// - [name] 输出源名称, 缺省: default
  Future<void> changeAudioSource({String name = "default"}) async {
    if (_isInit) {
      _audioSourceName = name;
      return MediasKitPlatform.instance
          .rtcLiveControllerChangeAudioSource(identifier, name);
    }
  }

  /// 切换音频输出源
  ///
  /// - [name] 输出源名称, 缺省: default
  ///
  /// - [volume] 输出音量, 缺省: 1.0
  Future<void> changeAudioSink({
    String name = "default",
    double volume = 1.0,
  }) async {
    if (_isInit) {
      _audioSinkName = name;
      _audioSinkVolume = volume;

      /// 扩音1.8倍
      return MediasKitPlatform.instance.rtcLiveControllerChangeAudioSink(
        identifier,
        name,
        volume * 1.8,
      );
    }
  }

  /// 释放资源, 标志结束直播
  Future<void> dispose() async {
    _isInit = false;
    _enableSpeak = false;
    return MediasKitPlatform.instance.rtcLiveControllerDispose(identifier);
  }
}
