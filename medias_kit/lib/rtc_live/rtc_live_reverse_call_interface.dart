/// 直播用户离开频道原因
enum RtcLiveUserLeftReason {
  /// 退出
  quit,

  /// 掉线
  offline,

  /// 角色改变, 仅表示用户从广播者变为观众
  roleChange
}

class RtcStats {
  /// 频道用户数
  final int userCount;

  /// 视频发送码率, 单位: kbps
  final int sendVideoBitrate;

  /// 发送丢包率
  final int sendLossRate;

  /// 系统 CPU 使用率 %
  final int cpuTotalUsage;

  /// 系统内存使用率 %
  final int memoryTotalUsage;

  /// 客户端到本地路由器的往返延时
  final int gatewayRtt;

  RtcStats({
    required this.userCount,
    required this.sendVideoBitrate,
    required this.sendLossRate,
    required this.cpuTotalUsage,
    required this.memoryTotalUsage,
    required this.gatewayRtt,
  });
}

/// 直播反向调用接口
abstract class RtcLiveReverseCallInterface {
  /// 用户加入频道
  final void Function(int userId)? onUserJoined;

  /// 用户离开频道
  final void Function(int userId, RtcLiveUserLeftReason reason)? onUserLeft;

  /// 传输状态
  final void Function(RtcStats rtcStats)? onTransportStats;

  /// 网络质量
  final void Function(int quality)? onNetworkQuality;

  /// 网络类型
  final void Function(int type)? onNetworkType;

  /// RTC连接丢失, 需要重新初始化
  final void Function()? onReInit;

  /// 构造函数
  ///
  /// - [onUserJoined] 默认值: 缺省
  ///
  /// - [onUserLeft] 默认值: 缺省
  ///
  /// - [onTransportStats] 默认值: 缺省
  ///
  /// - [onNetworkQuality] 默认值: 缺省
  ///
  /// - [onNetworkType] 默认值: 缺省
  RtcLiveReverseCallInterface({
    this.onUserJoined,
    this.onUserLeft,
    this.onTransportStats,
    this.onNetworkQuality,
    this.onNetworkType,
    this.onReInit,
  });
}
