import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_shaders/flutter_shaders.dart';

/// 着色器处理器
///
/// - [shader] 着色器
///
/// - [image] 纹理
///
/// - [size] 纹理大小
///
/// - [time] 运行时间
typedef ShaderHandler = void Function(
  FragmentShader shader,
  ui.Image image,
  Size size,
  double time,
);

/// 着色器资源
enum ShaderAssets {
  /// 高斯模糊 uRadius 推荐值: 50
  gaussianBlur,

  /// 像素化 uPixels 推荐值: (60, 60)
  pixelation,
}

/// 着色器文件路径扩展
extension ShaderPath on ShaderAssets {
  /// 着色器文件名
  static const _names = [
    "gaussian_blur.frag",
    "pixelation.frag",
  ];

  /// 默认着色器参数处理器
  static final _defaultHandlers = <ShaderHandler>[
    (
      FragmentShader shader,
      ui.Image image,
      Size size,
      double time,
    ) {
      shader.setFloatUniforms((value) {
        value.setFloat(50);
        value.setSize(size);
      });
      shader.setImageSampler(0, image);
    },
    (
      FragmentShader shader,
      ui.Image image,
      Size size,
      double time,
    ) {
      shader.setFloatUniforms((value) {
        value.setSize(const Size(60, 60));
        value.setSize(size);
      });
      shader.setImageSampler(0, image);
    },
  ];

  /// 自定义着色器参数处理器
  static final _handlers = <ShaderAssets, ShaderHandler?>{};

  /// 设置着色器参数处理器
  void setHandler(ShaderHandler? handler) {
    _handlers[this] = handler;
  }

  /// 着色器文件路径
  String get assetKey => "packages/medias_kit/assets/shaders/${_names[index]}";

  /// 着色器参数处理器
  ShaderHandler get handler => _handlers[this] ?? _defaultHandlers[index];
}
