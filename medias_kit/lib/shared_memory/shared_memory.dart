import 'dart:ffi' as ffi;
import 'dart:typed_data';
import 'package:ffi/ffi.dart';

import '../_ffigen/shared_memory_bindings.dart';

/// 共享内存类，提供跨进程数据共享功能。
///
/// 该类通过FFI（Foreign Function Interface）与底层共享内存实现进行交互，
/// 允许Dart应用程序创建、读取、写入和销毁共享内存区域。
/// 主要用于在不同进程间高效地共享二进制数据。
class SharedMemory {
  /// 指向底层共享内存区域的句柄
  late final ffi.Pointer<ffi.Void> _handle;

  /// 共享内存的唯一标识符
  ///
  /// 用于在不同进程间识别同一块共享内存区域
  final String key;

  /// 共享内存区域的大小（字节）
  final int size;

  /// 创建或连接到一个共享内存区域
  ///
  /// - [key] 共享内存的唯一标识符
  /// - [size] 共享内存区域的大小（字节）
  ///
  /// 如果创建失败，将抛出异常
  SharedMemory(this.key, this.size) {
    final keyUtf8 = key.toNativeUtf8();
    _handle = sharedMemoryOpen(keyUtf8.cast(), size);
    calloc.free(keyUtf8);

    if (_handle == ffi.nullptr) {
      throw Exception('Failed to create shared memory');
    }
  }

  /// 释放共享内存资源
  ///
  /// 当不再需要访问共享内存时，应调用此方法以释放系统资源
  void dispose() {
    final keyUtf8 = key.toNativeUtf8();
    sharedMemoryFree(keyUtf8.cast());
  }

  /// 向共享内存写入数据
  ///
  /// - [data] 要写入的二进制数据
  ///
  /// 返回写入操作是否成功。如果数据长度超过共享内存大小，将返回false
  bool write(Uint8List data) {
    if (data.length > size) return false;

    final buffer = calloc<ffi.Uint8>(data.length);
    buffer.asTypedList(data.length).setAll(0, data);

    final result = sharedMemoryWrite(
      _handle,
      buffer.cast(),
      data.length,
    );
    calloc.free(buffer);

    return result;
  }

  /// 从共享内存读取数据
  ///
  /// 返回读取的二进制数据。如果读取失败，返回null
  Uint8List? read() {
    final buffer = calloc<ffi.Uint8>(size);
    final result = sharedMemoryRead(_handle, buffer.cast(), size);

    if (!result) {
      calloc.free(buffer);
      return null;
    }

    final data = buffer.asTypedList(size).sublist(0);
    calloc.free(buffer);

    return data;
  }
}
