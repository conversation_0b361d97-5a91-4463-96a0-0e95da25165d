library medias_kit;

export 'core/medias_kit_types.dart';

export 'assets/shader_assets.dart';

export 'device/host_device.dart';

export 'light/light_controller.dart';

export 'monitor/monitor_controller.dart';
export 'monitor/monitor_view.dart';

export 'record/record_controller.dart';

export 'rtc_live/rtc_live_controller.dart';
export 'rtmp_live/rtmp_live_controller.dart';

export 'model/model_controller.dart';
export 'model/model_view.dart';

export 'ble/peripheral_controller.dart';

export 'voice/voice_helper.dart';
export 'shared_memory/shared_memory.dart';

import 'core/medias_kit_platform_interface.dart';

/// 媒体库
class MediasKit {
  MediasKit._();

  static String? _pluginId;

  /// 插件 ID
  static get pluginId {
    if (_pluginId == null) {
      throw Exception("MediasKit not initialized");
    }
    return _pluginId;
  }

  /// 插件初始化, 必须在使用任何功能之前调用一次
  static Future<void> ensureInitialized() async {
    _pluginId = await MediasKitPlatform.instance.bindNativePlugin();
  }
}
