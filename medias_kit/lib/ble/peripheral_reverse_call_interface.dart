import 'dart:collection';

/// 外设接收数据
class AcceptData {
  final String msgId;
  final int msgType;

  final int msgTotal;

  final int msgIndex;

  String data;

  //接收端用以粘包处理，非协议字段
  final SplayTreeMap<int, String> dataBuffer = SplayTreeMap<int, String>();

  AcceptData({
    required this.msgId,
    required this.msgType,
    required this.msgTotal,
    required this.msgIndex,
    this.data = "",
  });

  @override
  String toString() {
    return "${{
      "msgId": msgId,
      "msgType": msgType,
      "msgTotal": msgTotal,
      "msgIndex": msgIndex,
      "data": data
    }}";
  }
}

class DeviceConnectionState {
  final bool connected;
  final bool adapterState;
  final String? deviceName;
  final String? deviceAddress;
  final String? adapterAddress;
  final String adapterName;
  DeviceConnectionState(
      {required this.connected,
      this.adapterState = false,
      this.deviceName,
      this.deviceAddress,
      this.adapterAddress,
      this.adapterName = "SurgSmart-default"});

  @override
  String toString() {
    return "${{
      "connected": connected,
      "adapterState": adapterState,
      "deviceName": deviceName,
      "deviceAddress": deviceAddress,
      "adapterAddress": adapterAddress,
      "adapterName": adapterName
    }}";
  }
}

/// BLE适配器信息
class AdapterInfo {
  final String? name;
  final String? alias;
  //mac地址
  final String address;
  //是否可见
  final bool discoverable;
  //是否开启
  final bool powered;

  AdapterInfo(
      {required this.address,
      this.discoverable = false,
      this.powered = false,
      this.name,
      this.alias});

  @override
  String toString() {
    return "${{
      "name": name,
      "address": address,
      "discoverable": discoverable,
      "powered": powered,
      "alias": alias,
    }}";
  }
}

/// BLE外设反向调用接口
abstract class PeripheralReverseCallInterface {
  /// 写入监听
  void Function(AcceptData acceptInfo)? accept;

  /// 连接状态
  void Function(DeviceConnectionState state)? connectionState;

  /// - [onAccept] 默认值: 缺省
  /// - [connectionState] 默认值: 缺省
  PeripheralReverseCallInterface({
    this.accept,
    this.connectionState,
  });
}
