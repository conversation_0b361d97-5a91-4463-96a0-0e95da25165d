import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:medias_kit/ble/peripheral_reverse_call_interface.dart';
import '../core/medias_kit_platform_interface.dart';

/// BLE外设控制器
class PeripheralController extends PeripheralReverseCallInterface {
  /// 控制器标志符
  final String identifier = 'PeripheralController.${UniqueKey()}';
  int mtu = 20;

  /// 是否初始化
  bool _isInit = false;

  //存储连接状态提供外部
  DeviceConnectionState? _connectionState;
  bool get isConnect => _connectionState?.connected ?? false;

  //存储蓝牙客户端写入的消息
  //使用map解决多类型多包消息同步接收
  //有序Map便于控制缓存数据量
  final LinkedHashMap<String, AcceptData> _acceptDataMap =
      LinkedHashMap<String, AcceptData>();

  /// 外部写入监听
  void Function(AcceptData acceptData)? acceptListener;

  /// 外部连接状态监听
  void Function(DeviceConnectionState connectionState)? connectionStateListener;

  PeripheralController._() {
    super.accept = _acceptCall;
    super.connectionState = _connectionStateCall;
  }

  static final PeripheralController instance = PeripheralController._();
  factory PeripheralController() => instance;

  //队列发送提高传输可靠性，消除极端情况隐患
  final Queue<String> sendQueue = Queue();
  Timer? sendTask;
  //优化发送间隔，减少timer开销
  int lastSendTime = 0;
  //消息发送间隔
  static const int sendInterval = 20;

  void checkMtu(AcceptData acceptData) {
    if (acceptData.msgType == 0) {
      int? mtuSize;
      try {
        dynamic dataMap = jsonDecode(acceptData.data);
        if (dataMap is Map) {
          mtuSize = dataMap["mtu"];
        }
      } catch (e) {
        debugPrint("PeripheralController ble accept error: ${e.toString()}");
      }

      if (mtuSize == null) {
        instance.stop();
        return;
      }

      if (mtuSize > 20) {
        instance.mtu = mtuSize - 3; //去除协议头保留有效传输容量大小
      }
      debugPrint("PeripheralController ble mtu: ${instance.mtu}}");

      //回复连接建立完成
      instance.notify(msgType: acceptData.msgType, data: '''{"res":true}''');
    }
  }

  // 底层BLE写入回调
  void _acceptCall(AcceptData acceptData) {
    debugPrint("PeripheralController ble accept: ${acceptData.toString()}");

    if (acceptData.msgIndex > acceptData.msgTotal ||
        acceptData.msgIndex == 0 ||
        acceptData.msgTotal == 0) {
      //客户端数据容错处理
      return;
    }
    //内部做粘包处理提供外部
    AcceptData currentMsgData =
        instance._acceptDataMap.putIfAbsent(acceptData.msgId, () => acceptData);
    if (acceptData.msgTotal == 1) {
      // 单包
      checkMtu(acceptData);
      instance.acceptListener?.call(acceptData);
      return;
    }

    currentMsgData.dataBuffer[acceptData.msgIndex] = acceptData.data;
    currentMsgData.data = ""; //多包未接收完成避免外部错误使用
    if (currentMsgData.dataBuffer.length == acceptData.msgTotal) {
      // 全部接收完成,组合数据
      StringBuffer dataBuffer = StringBuffer();
      dataBuffer.writeAll(currentMsgData.dataBuffer.values);
      currentMsgData.data = dataBuffer.toString();
      checkMtu(currentMsgData);
      // 完成回调
      instance.acceptListener?.call(currentMsgData);

      // 清除10条数据前的缓存数据
      if (instance._acceptDataMap.length > 10) {
        instance._acceptDataMap.remove(instance._acceptDataMap.keys.first);
      }
    }
  }

  // 底层连接状态回调
  void _connectionStateCall(DeviceConnectionState connectionState) {
    instance._connectionState = connectionState;
    instance.connectionStateListener?.call(connectionState);
  }

  /// 获取设备连接信息
  DeviceConnectionState? getDeviceConnectionInfo() {
    return instance._connectionState;
  }

  void setDataAcceptListener(Function(AcceptData acceptData)? function) {
    acceptListener = function;
  }

  void setConnectionStateListener(
      Function(DeviceConnectionState connectionState)? function) {
    connectionStateListener = function;
  }

  /// 初始化资源
  Future<bool> init() async {
    if (_isInit) return true;
    _isInit = await MediasKitPlatform.instance
            .peripheralControllerInit(this, identifier) ??
        false;
    return _isInit;
  }

  /// 启动BLE自定义服务和广播并回调连接信息，如已启动则不会重复启动
  Future<void> start() async {
    if (_isInit) {
      return MediasKitPlatform.instance.peripheralControllerStart(identifier);
    }
  }

  /// 通知
  Future<void> notify({
    required int msgType,
    required String data,
    bool priority = false,
  }) async {
    if (!_isInit) {
      return;
    }
    if (instance._connectionState?.connected != true) {
      return;
    }
    List<int> dataBytes = utf8.encode(data);

    int pkgBytes = msgType.toString().length + 10;
    if ((dataBytes.length + pkgBytes) <= mtu) {
      //小程序BLE最低MTU为20字节，json格式占用较大，为提高设备兼容性自定义String数据格式协议
      //消息id(数字加字母，两位)-当前MsgIndex(int)-总消息条数(int 最大两位)-msgId(int 最大两位)-jsonData
      String notifyData = "${createMsgId(2)}-1-1-$msgType-$data"; //
      debugPrint('单包，send notify: $notifyData');

      _sendNotify(
        notifyData,
        priority: priority,
      );
    } else {
      debugPrint('多包，send notify: $data');
      if (mtu < pkgBytes) {
        return;
      }
      // 分包
      List<String> packageList = subPackage(data, pkgBytes);

      if (packageList.length > 99) {
        debugPrint('数据大小超出限制: $data');
        return;
      }
      String msgId = createMsgId(2);
      for (int i = 0; i < packageList.length; i++) {
        String notifyData =
            "$msgId-${i + 1}-${packageList.length}-$msgType-${packageList[i]}";
        _sendNotify(
          notifyData,
          priority: priority,
        );
      }
    }
  }

  /// 主机断开连接
  Future<void> stop() async {
    if (_isInit) {
      return MediasKitPlatform.instance.peripheralControllerStop(identifier);
    }
  }

  /// 释放资源,目前为单例，不提供外部调用
  // Future<void> dispose() async {
  //   if (_isInit) {
  //     _isInit = false;
  //     return MediasKitPlatform.instance.peripheralControllerDispose(identifier);
  //   }
  // }

  //创建消息id用于分包组合
  String createMsgId(int length) {
    final Random random = Random();
    const String characters =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    String msgId = '';
    for (int i = 0; i < length; i++) {
      final int randomIndex = random.nextInt(characters.length);
      msgId += characters[randomIndex];
    }
    return msgId;
  }

  List<String> subPackage(String data, int pkgBytes) {
    int fragmentLength = mtu - pkgBytes;
    List<String> words = data.split('');
    List<String> fragmentDataList = [];
    StringBuffer fragmentBuffer = StringBuffer();
    for (int i = 0; i < words.length; i++) {
      fragmentBuffer.write(words[i]);
      String tempWords = fragmentBuffer.toString();
      if (utf8.encode(tempWords).length > fragmentLength) {
        fragmentDataList.add(tempWords.substring(0, tempWords.length - 1));
        fragmentBuffer.clear();
        fragmentBuffer.write(words[i]);
      }
    }
    String endWords = fragmentBuffer.toString();
    if (endWords.isNotEmpty) {
      fragmentDataList.add(endWords);
    }
    return fragmentDataList;
  }

  //消息发送统一管理
  void _sendNotify(String notifyData, {bool priority = false}) {
    if (sendTask == null || sendTask?.isActive == false) {
      if (DateTime.now().millisecondsSinceEpoch - lastSendTime > sendInterval) {
        MediasKitPlatform.instance
            .peripheralControllerNotify(identifier, notifyData);
        lastSendTime = DateTime.now().millisecondsSinceEpoch;
        debugPrint('单包-send notify: $notifyData');
      } else {
        if (priority) {
          sendQueue.addFirst(notifyData);
        } else {
          sendQueue.addLast(notifyData);
        }
        sendTask?.cancel();
        sendTask =
            Timer.periodic(const Duration(milliseconds: sendInterval), (timer) {
          if (sendQueue.isEmpty) {
            timer.cancel();
            sendTask = null;
            return;
          }
          MediasKitPlatform.instance
              .peripheralControllerNotify(identifier, sendQueue.first);
          lastSendTime = DateTime.now().millisecondsSinceEpoch;
          debugPrint('包-send notify: ${sendQueue.first}');

          sendQueue.removeFirst();
        });
      }
    } else {
      if (priority) {
        sendQueue.addFirst(notifyData);
      } else {
        sendQueue.addLast(notifyData);
      }
    }
  }
}
