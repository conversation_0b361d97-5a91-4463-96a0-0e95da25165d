import 'dart:async';

import 'package:flutter/material.dart';

import '../assets/shader_assets.dart';
import '../core/medias_kit_platform_interface.dart';
import '../device/host_device.dart';

class MonitorState {
  /// OpenGL 纹理 ID
  final int textureId;

  final bool isLoading;

  final bool isFailure;

  final bool isSuccess;

  const MonitorState.loading()
      : textureId = 0,
        isLoading = true,
        isFailure = false,
        isSuccess = false;

  const MonitorState.failure()
      : textureId = 0,
        isLoading = false,
        isFailure = true,
        isSuccess = false;

  const MonitorState.success({required this.textureId})
      : isLoading = false,
        isFailure = false,
        isSuccess = true;
}

/// 画面采集监视控制器
class MonitorController {
  /// 控制器标志符
  final String identifier = 'MonitorController.${UniqueKey()}';

  /// 视频采集设备的名称, 缺省: 为内置 PCI 接口设备
  ///
  /// - [HostDevice.videoSources] 中获取
  final VideoDevice? videoDevice;

  /// 采集宽度
  final int width;

  /// 采集高度
  final int height;

  /// 采集帧率
  final int framerate;

  /// 构造函数
  ///
  /// - [videoDevice] 默认值: 缺省
  ///
  /// - [width] 默认值: 1920
  ///
  /// - [height] 默认值: 1080
  ///
  /// - [framerate] 默认值: 60
  MonitorController({
    this.videoDevice,
    this.width = 1920,
    this.height = 1080,
    this.framerate = 60,
  });

  var _state = const MonitorState.loading();

  /// 获取当前状态
  MonitorState get state => _state;

  /// 设置当前状态
  set state(MonitorState value) {
    _state = value;
    isNeedRefresh.value = true;
  }

  ShaderAssets? _shader;

  /// 获取着色器资源
  ShaderAssets? get shader => _shader;

  /// 设置着色器资源
  set shader(ShaderAssets? value) {
    _shader = value;
    isNeedRefresh.value = true;
  }

  /// 需要刷新 UI
  final isNeedRefresh = ValueNotifier<bool>(false);

  /// 初始化资源
  Future<bool> init() async {
    if (state.isSuccess) return true;
    final textureId = await MediasKitPlatform.instance.monitorControllerInit(
      identifier,
      videoDevice,
      width,
      height,
      framerate,
    );
    if (textureId != null) {
      state = MonitorState.success(textureId: textureId);
    } else {
      state = const MonitorState.failure();
    }
    return state.isSuccess;
  }

  /// 开始
  Future<void> start() async {
    if (state.isSuccess) {
      return MediasKitPlatform.instance.monitorControllerStart(identifier);
    }
  }

  /// 停止
  Future<void> stop() async {
    if (state.isSuccess) {
      return MediasKitPlatform.instance.monitorControllerStop(identifier);
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    isNeedRefresh.dispose();
    return MediasKitPlatform.instance.monitorControllerDispose(identifier);
  }
}
