import 'package:flutter/material.dart';
import 'package:flutter_shaders/flutter_shaders.dart';
import 'package:flutter/scheduler.dart';

import '../assets/shader_assets.dart';
import 'monitor_controller.dart';

/// 画面采集监视播放组件
class MonitorView extends StatefulWidget {
  /// 监视控制器
  final MonitorController monitorController;

  /// 构造函数
  ///
  /// - [monitorController] 监视控制器
  const MonitorView({super.key, required this.monitorController});

  @override
  State<MonitorView> createState() => _MonitorViewState();
}

class _MonitorViewState extends State<MonitorView> {
  void updateState() {
    if (mounted && widget.monitorController.isNeedRefresh.value) {
      widget.monitorController.isNeedRefresh.value = false;
      setState(() {});
    }
  }

  @override
  void initState() {
    widget.monitorController.isNeedRefresh.addListener(updateState);
    super.initState();
  }

  @override
  void dispose() {
    widget.monitorController.isNeedRefresh.removeListener(updateState);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio:
          widget.monitorController.width / widget.monitorController.height,
      child: Container(
        color: Colors.black,
        child: widget.monitorController.state.isSuccess
            ? content
            : Center(
                child: Text(
                  widget.monitorController.state.isFailure
                      ? "监视器初始化失败..."
                      : "监视器正在初始化...",
                  style: const TextStyle(color: Colors.white),
                ),
              ),
      ),
    );
  }

  Widget get content {
    final shaderAsset = widget.monitorController.shader;
    final textureId = widget.monitorController.state.textureId;
    if (shaderAsset == null) {
      return Texture(textureId: textureId);
    } else {
      return ShaderBuilder(
        (context, shader, child) {
          return _TickingBuilder(
            builder: (context, time) {
              return AnimatedSampler(
                (image, size, canvas) {
                  shaderAsset.handler(shader, image, size, time);
                  canvas.drawRect(Offset.zero & size, Paint()..shader = shader);
                },
                child: Texture(textureId: textureId),
              );
            },
          );
        },
        assetKey: shaderAsset.assetKey,
      );
    }
  }
}

class _TickingBuilder extends StatefulWidget {
  final Widget Function(BuildContext context, double time) builder;

  const _TickingBuilder({required this.builder});

  @override
  State<_TickingBuilder> createState() => _TickingBuilderState();
}

class _TickingBuilderState extends State<_TickingBuilder>
    with SingleTickerProviderStateMixin {
  late final Ticker _ticker;
  double _time = 0.0;

  @override
  void initState() {
    super.initState();
    _ticker = createTicker(_handleTick)..start();
  }

  @override
  void dispose() {
    _ticker.dispose();
    super.dispose();
  }

  void _handleTick(Duration elapsed) {
    setState(() => _time = elapsed.inMilliseconds.toDouble() / 1000.0);
  }

  @override
  Widget build(BuildContext context) => widget.builder(context, _time);
}
