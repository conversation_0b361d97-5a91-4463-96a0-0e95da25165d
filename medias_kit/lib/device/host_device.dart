import 'dart:async';
import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';

import '../_ffigen/device_bindings.dart';
import '../_ffigen/vector_bindings.dart';
import '../core/medias_kit_platform_interface.dart';

class AudioDevice {
  /// 默认设备
  final bool isDefault;

  /// 设备索引
  final int index;

  /// 设备名称
  final String name;

  /// 设备描述
  final String description;

  /// 设备音量
  int volume;

  /// 设备静音
  bool mute;

  /// 构造函数
  AudioDevice({
    required this.isDefault,
    required this.index,
    required this.name,
    required this.description,
    required this.volume,
    required this.mute,
  });

  Map<String, dynamic> toMap() => {
        'isDefault': isDefault,
        'index': index,
        'name': name,
        'description': description,
        'volume': volume,
        'mute': mute,
      };

  @override
  String toString() => toMap().toString();
}

class VideoDevice {
  /// 设备索引
  final int index;

  /// 设备名称
  final String name;

  /// 设备路径
  final String path;

  /// 是否为USB扩展
  final bool isUsbExtend;

  VideoDevice({
    required this.index,
    required this.name,
    required this.path,
    required this.isUsbExtend,
  });

  Map<String, dynamic> toMap() => {
        'index': index,
        'name': name,
        'path': path,
        'isUsbExtend': isUsbExtend,
      };

  @override
  String toString() => toMap().toString();
}

class HostDevice {
  HostDevice._();

  static final share = HostDevice._(); 

  /// 音频输入源
  final audioSources = ValueNotifier<List<AudioDevice>>([]);

  /// 音频输出源
  final audioSinks = ValueNotifier<List<AudioDevice>>([]);

  /// 视频输入源
  final videoSources = ValueNotifier<List<VideoDevice>>([]);

  /// 获取运行环境平台信息
  String getPlatform() {
    return getHostDevicePlatform().cast<Utf8>().toDartString();
  }

  /// 使用指定磁盘空间信息来构建自定义对象
  T buildingWithSpaceInfo<T>({
    String path = "/",
    required T Function(HostDeviceSpaceInfo spaceInfo) building,
  }) {
    final nativePath = path.toNativeUtf8();
    final spaceInfo = getHostDeviceSpaceInfo(nativePath.cast());
    malloc.free(nativePath);
    final ret = building(spaceInfo.ref);
    malloc.free(spaceInfo);
    return ret;
  }

  /// 获取视频输入源
  List<String> getVideoCaptures() {
    final names = <String>[];
    final vector = getVideoCaptureDevices();
    final size = vectorSize(vector);
    for (var i = 0; i < size; i++) {
      final name = vectorElementAt(vector, i).cast<Utf8>().toDartString();
      names.add(name);
    }
    vectorDestroy(vector, true);
    return names;
  }

  /// 设置输入设备音量
  ///
  /// - [name] 设备名
  ///
  /// - [volume] 音量 MAX: 65535
  Future<void> setAudioSourceVolume(String name, int volume) {
    return MediasKitPlatform.instance.setAudioSourceVolume(name, volume);
  }

  /// 设置输出设备音量
  ///
  /// - [name] 设备名
  ///
  /// - [volume] 音量 MAX: 65535
  Future<void> setAudioSinkVolume(String name, int volume) {
    return MediasKitPlatform.instance.setAudioSinkVolume(name, volume);
  }

  /// 设置设备是否网络在线, 需根据网络状态及时更新
  Future<void> setOnline(bool isOnline) {
    return MediasKitPlatform.instance.setOnline(isOnline);
  }
}
