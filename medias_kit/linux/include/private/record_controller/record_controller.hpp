#pragma once

#include <cstdint>
#include <mutex>

#include "codec/audio_encoder.hpp"
#include "codec/video_encoder.hpp"
#include "mw_mp4/mw_mp4.h"
#include "stream&capture/audio_capture.hpp"
#include "stream&capture/video_capture.hpp"
#include "utils/flutter_api.hpp"

namespace MK {
class RecordController {
  private:
    VideoCapture *const videoCapture;

    AudioCapture *const audioCapture;

    VideoEncoder *const videoEncoder;

    AudioEncoder *const audioEncoder;

    std::string baseDir;

    std::string spaceDir;

    std::string recordDir;

    std::string streamDir;

    std::string playbackDir;

    std::string filePath;

    std::string indexPath;

    std::string breakOffsetPath;

    int index = 1;

    int packageCount = 0;

    uint64_t breakOffset = 0;

    uint64_t firstFrameTimestamp = 0;

    std::mutex mtx;

    mw_mp4_handle_t handle = nullptr;

    bool pause = false;

    bool enableRecordAudio = false;

    bool isInit = false;

    void
    encodeVideoFrame(const uint8_t *const frame, long frameLen);

    void
    encodeAudioFrame(char *frame, long frameLen);

    void
    writeVideoPackage(
        const uint8_t *package,
        uint32_t packageLen,
        mw_venc_frame_info_t *packageInfo
    );

    void
    writeAudioPackage(
        const uint8_t *package,
        uint32_t packageLen,
        uint64_t pts
    );

    bool
    freeDiskSpace();

    bool
    newMp4();

    void
    closeMp4(const bool finishRecord);

  public:
    enum class Event {
        init,
        start,
        stop,
        recordAudio,
        changeAudioSource,
        merge,
        frameCount,
        firstFrameTimestampWithOffset,
        dispose
    };

    enum class ReverseEvent {
        merged,
        captureSignal,
    };

    const std::shared_ptr<FlutterApi> flutterApi;

    const std::shared_ptr<const std::string> identifier;

    const std::shared_ptr<const std::string> spaceName;

    RecordController(
        const std::shared_ptr<FlutterApi> flutterApi,
        const std::shared_ptr<const std::string> identifier,
        const std::shared_ptr<const std::string> spaceName,
        const std::shared_ptr<const VideoCaptureDevice> videoDevice,
        const std::shared_ptr<const std::string> audioCaptureName,
        const int width,
        const int height,
        const int framerate,
        const int videoBitrate,
        const int sampleRate,
        const int audioBitrate,
        const int numChannels
    );

    ~RecordController();

    bool
    init();

    void
    start();

    void
    stop();

    void
    recordAudio(bool enable);

    bool
    updateAudioCapture(
        const std::shared_ptr<const std::string> audioCaptureName
    );

    void
    recordRemoteAudio(char *frame, int frameLen);

    void
    merge(
        const std::shared_ptr<const std::string> name,
        const long beginTime,
        const long endTime,
        const bool needEndPrecision
    ) const;

    int64_t
    frameCount() const;

    int64_t
    firstFrameTimestampWithOffset();
};
} // namespace MK
