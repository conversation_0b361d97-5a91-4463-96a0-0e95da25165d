#pragma once

#include <flutter_linux/flutter_linux.h>
#include <string>

namespace MK {
class FlutterApi {
    struct UserData {
        FlMethodChannel *const channel;

        std::string *const method;

        FlValue *const arguments;

        UserData(
            FlMethodChannel *const channel,
            std::string *const method,
            FlValue *const arguments
        );

        ~UserData();
    };

    FlMethodChannel *const channel;

  public:
    FlutterApi(FlMethodChannel *const channel);

    ~FlutterApi();

    void
    call(std::string *const method, FlValue *const arguments) const;

    void
    safeCall(std::string *const method, FlValue *const arguments);
};
} // namespace MK