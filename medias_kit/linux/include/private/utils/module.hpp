#pragma once

#include <memory>
#include <string>

namespace MK {
/// 模块解析对象
class Module {
  public:
    /// 模块名称
    const std::string name;

    /// 目标事件
    const int event;

    /**
     * @brief Construct a new Module object
     *
     * @param name 模块名称
     * @param event 目标事件
     */
    Module(const std::string &name, const int event);

    /**
     * @brief 解析来源构造模块
     *
     * @param source 来源
     * @return const std::shared_ptr<Module>
     */
    static const std::shared_ptr<Module>
    parse(const std::string &source);
};
} // namespace MK