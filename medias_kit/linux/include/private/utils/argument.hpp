#pragma once

#include <cstdint>
#include <flutter_linux/flutter_linux.h>
#include <memory>
#include <string>

namespace MK {
class Argument {
    FlValue *value;

  public:
    Argument(FlValue *value);

    const std::shared_ptr<const std::string>
    getString(const std::string key) const;

    const int64_t
    getInt(const std::string key) const;

    const double
    getFloat(const std::string key) const;

    const bool
    getBool(const std::string key) const;

    const std::shared_ptr<Argument>
    getSubArgument(const std::string key) const;
};
} // namespace MK