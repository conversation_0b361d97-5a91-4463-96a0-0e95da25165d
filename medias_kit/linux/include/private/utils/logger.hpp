#pragma once

#include <spdlog/logger.h>

namespace MK {

#define LOG_T(fmt, args...) Log.t(__FILE__, __LINE__, __func__, fmt, args)

#define LOG_D(fmt, args...) Log.d(__FILE__, __LINE__, __func__, fmt, args)

#define LOG_I(fmt, args...) Log.i(__FILE__, __LINE__, __func__, fmt, args)

#define LOG_W(fmt, args...) Log.w(__FILE__, __LINE__, __func__, fmt, args)

#define LOG_E(fmt, args...) Log.e(__FILE__, __LINE__, __func__, fmt, args)

#define LOG_F(fmt, args...) Log.f(__FILE__, __LINE__, __func__, fmt, args)

class Logger {

    std::shared_ptr<spdlog::logger> logger;

    Logger();

  public:
    static const Logger &
    instance();

    template <typename T>
    void
    t(const std::string &file,
      const int line,
      const std::string &func,
      const T &msg) {
        logger->trace(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            msg
        );
    }

    template <typename... Args>
    void
    t(const std::string &file,
      const int line,
      const std::string &func,
      const std::string &fmt,
      const Args &...args) const {
        logger->trace(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            fmt::format(fmt, args...)
        );
    }

    template <typename T>
    void
    d(const std::string &file,
      const int line,
      const std::string &func,
      const T &msg) {
        logger->debug(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            msg
        );
    }

    template <typename... Args>
    void
    d(const std::string &file,
      const int line,
      const std::string &func,
      const std::string &fmt,
      const Args &...args) const {
        logger->debug(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            fmt::format(fmt, args...)
        );
    }

    template <typename T>
    void
    i(const std::string &file,
      const int line,
      const std::string &func,
      const T &msg) {
        logger->info(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            msg
        );
    }

    template <typename... Args>
    void
    i(const std::string &file,
      const int line,
      const std::string &func,
      const std::string &fmt,
      const Args &...args) const {
        logger->info(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            fmt::format(fmt, args...)
        );
    }

    template <typename T>
    void
    w(const std::string &file,
      const int line,
      const std::string &func,
      const T &msg) {
        logger->warn(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            msg
        );
    }

    template <typename... Args>
    void
    w(const std::string &file,
      const int line,
      const std::string &func,
      const std::string &fmt,
      const Args &...args) const {
        logger->warn(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            fmt::format(fmt, args...)
        );
    }

    template <typename T>
    void
    e(const std::string &file,
      const int line,
      const std::string &func,
      const T &msg) {
        logger->error(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            msg
        );
    }

    template <typename... Args>
    void
    e(const std::string &file,
      const int line,
      const std::string &func,
      const std::string &fmt,
      const Args &...args) const {
        logger->error(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            fmt::format(fmt, args...)
        );
    }

    template <typename T>
    void
    f(const std::string &file,
      const int line,
      const std::string &func,
      const T &msg) {
        logger->critical(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            msg
        );
    }

    template <typename... Args>
    void
    f(const std::string &file,
      const int line,
      const std::string &func,
      const std::string &fmt,
      const Args &...args) const {
        logger->critical(
            R"("stackTrace":"{}:{}", "func":"{}", "message":"{}")",
            file.substr(file.rfind("medias_kit")),
            line,
            func,
            fmt::format(fmt, args...)
        );
    }
};

extern const Logger &Log;
} // namespace MK