#pragma once

#include <memory>
#include <string>
#include <sys/ipc.h>
#include <sys/shm.h>

namespace MK {

class SharedMemory {
  private:
    int shmId;

    void *shmAddr;

    size_t shmSize;

    std::string shmKey;

  public:
    SharedMemory(const std::string &key, const size_t size);

    ~SharedMemory();

    bool
    openOrCreate();

    bool
    attach();

    bool
    detach();

    bool
    destroy();

    const void *
    getData() const {
        return shmAddr;
    }

    size_t
    getSize() const {
        return shmSize;
    }

    bool
    write(const void *data, size_t size);

    bool
    read(void *buffer, size_t size);
};

} // namespace MK