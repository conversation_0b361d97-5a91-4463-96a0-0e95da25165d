#ifndef SHARED_MEMORY_FFI_H
#define SHARED_MEMORY_FFI_H

#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>

#include "types/attributes_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 共享内存引用类型
typedef void *SharedMemoryRef;

/// 事件回调函数类型
typedef void (*OnEventCallback)(int code);

/// 打开共享内存
/// - [key] 共享内存的唯一标识符
/// - [size] 共享内存的大小
/// 返回共享内存引用
ATTRIBUTES SharedMemoryRef
sharedMemoryOpen(const char *key, const size_t size);

/// 释放共享内存
/// - [handle] 共享内存段的句柄
ATTRIBUTES void
sharedMemoryFree(const char *key);

/// 读取共享内存, 返回值表示是否成功
/// - [memory] 共享内存引用
/// - [buffer] 读取数据的缓冲区
/// - [size] 要读取的数据大小
ATTRIBUTES bool
sharedMemoryRead(SharedMemoryRef memory, void *buffer, const size_t size);

/// 写入共享内存, 返回值表示是否成功
/// - [memory] 共享内存引用
/// - [data] 要写入的数据
/// - [size] 要写入的数据大小
ATTRIBUTES bool
sharedMemoryWrite(SharedMemoryRef memory, const void *data, const size_t size);

#ifdef __cplusplus
}
#endif

#endif // SHARED_MEMORY_FFI_H