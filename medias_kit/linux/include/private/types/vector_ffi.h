#ifndef VECTOR_FFI_H
#define VECTOR_FFI_H

#include <stdbool.h>

#include "attributes_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 向量引用类型
typedef void *VectorRef;

/// 创建向量
/// 返回向量引用
ATTRIBUTES VectorRef
vectorCreate();

/// 销毁向量
/// - [vector] 向量引用
/// - [freeElements] 是否释放向量元素
ATTRIBUTES void
vectorDestroy(VectorRef vector, bool freeElements);

/// 获取向量元素数量
/// - [vector] 向量引用
ATTRIBUTES int
vectorSize(VectorRef vector);

/// 获取向量元素
/// - [vector] 向量引用
/// - [index] 元素索引
ATTRIBUTES void *
vectorElementAt(VectorRef vector, int index);

#ifdef __cplusplus
}
#endif

#endif // VECTOR_FFI_H
