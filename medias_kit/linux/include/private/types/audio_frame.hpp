#pragma once

#include <cstdint>
#include <cstring>

#include "utils/logger.hpp"

namespace MK {

class AudioFrame {
  public:
    const char *const buffer;

    const int length;

    const int64_t timestamp;

    AudioFrame(const char *buffer, const int length, const int64_t timestamp)
        : buffer(new char[length]()), length(length), timestamp(timestamp) {
        memcpy(const_cast<char *>(this->buffer), buffer, length);
    }

    AudioFrame(AudioFrame &&frame)
        : buffer(frame.buffer),
          length(frame.length),
          timestamp(frame.timestamp) {
        const_cast<char *&>(frame.buffer) = nullptr;
        const_cast<int &>(frame.length) = 0;
        const_cast<int64_t &>(frame.timestamp) = 0;
    }

    ~AudioFrame() {
        if (buffer) {
            delete[] buffer;
        }
    }
};

} // namespace MK