#pragma once

namespace MK {

class VideoStream {
  public:
    const long timestamp;

    const bool isKeyFrame;

    const int delay;

    const int size;

    VideoStream(
        const long timestamp,
        const bool isKeyFrame,
        const int delay,
        const int size
    )
        : timestamp(timestamp),
          isKeyFrame(isKeyFrame),
          delay(delay),
          size(size) {}
};
} // namespace MK