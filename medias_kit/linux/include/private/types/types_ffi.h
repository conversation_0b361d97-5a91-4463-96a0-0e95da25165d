#ifndef TYPES_FFI_H
#define TYPES_FFI_H

#include "attributes_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 像素格式枚举, 定义了视频帧的像素格式类型
///
/// - [pf_nv12]: YUV 4:2:0 格式，Y 平面后跟交错的 UV 平面
/// - [pf_i420]: YUV 4:2:0 格式，又称 YU12，三个独立平面 Y、U、V
/// - [pf_rgba]: RGB 格式，每像素 4 字节，包含透明通道
enum PixelFormat {
    /// NV12 格式 (YUV 4:2:0，Y 平面后跟交错的 UV 平面)
    pf_nv12,

    /// I420 格式 (YUV 4:2:0，三个独立平面 Y、U、V)
    pf_i420,

    /// RGBA 格式 (每像素 4 字节，包含透明通道)
    pf_rgba,
};

/// 核心事件枚举, 定义了核心模块的事件类型
enum CoreEvent {
    /// 绑定原生插件
    ce_bindNativePlugin,
};

#ifdef __cplusplus
}
#endif

#endif // TYPES_FFI_H
