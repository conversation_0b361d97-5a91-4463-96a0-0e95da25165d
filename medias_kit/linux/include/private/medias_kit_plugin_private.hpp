#pragma once

#include "medias_kit/medias_kit_plugin.h"
#include "types/types_ffi.h"
#include "utils/argument.hpp"

// This file exposes some plugin internals for unit testing. See
// https://github.com/flutter/flutter/issues/88724 for current limitations
// in the unit-testable API.

namespace MK {
/**
 * @brief 获取插件对象
 *
 * @param pluginId 插件ID
 * @return const MediasKitPlugin& 插件对象
 */
const MediasKitPlugin &
getPlugin(const std::string pluginId);

/**
 * @brief 公共模块消息处理
 *
 * @param plugin 插件句柄
 * @param event 消息事件
 * @param argument 消息参数
 * @return FlMethodResponse* 数据回执
 */
FlMethodResponse *
coreMessageHandler(
    const MediasKitPlugin &plugin,
    const CoreEvent event,
    const std::shared_ptr<const Argument> argument
);

} // namespace MK
