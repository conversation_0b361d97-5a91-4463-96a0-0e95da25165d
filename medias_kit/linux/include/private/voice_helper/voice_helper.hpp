#pragma once

#include <atomic>
#include <memory>
#include <string>
#include <vector>

#include "aikit/aikit_biz_api.h"
#include "aikit/aikit_biz_config.h"
#include "aikit/aikit_constant.h"
#include "renderer/audio_renderer.hpp"
#include "stream&capture/audio_capture.hpp"
#include "utils/flutter_api.hpp"

namespace MK {

class VoiceHelper {

  public:
    enum class Event {
        prepare,
        disable,
        speaking,
        waitingCommand,
    };

    enum class ReverseEvent {
        onAwakened,
        onRecognizing,
        onCommand,
    };

    enum class VoiceCommandState {
        success,
        disconnect,
    };

  private:
    static void
    onOutput(
        AIKIT_HANDLE *handle,
        const AIKIT_OutputData *output
    );

    static void
    onEvent(
        AIKIT_HANDLE *handle,
        AIKIT_EVENT eventType,
        const AIKIT_OutputEvent *eventValue
    );

    static void
    onError(
        AIKIT_HANDLE *handle,
        int32_t err,
        const char *desc
    );

    VoiceHelper() = default;

    ~VoiceHelper() = default;

    std::shared_ptr<FlutterApi> flutterApi;

    std::shared_ptr<const std::string> workDir;

    std::shared_ptr<const std::string> assetsDir;

    AIKIT_HANDLE *ivwHandle = nullptr;

    AIKIT_HANDLE *aisoundHandle = nullptr;

    AIKIT_HANDLE *esrHandle = nullptr;

    AIKIT::AIKIT_DataBuilder *ivwDataBuilder = nullptr;

    AIKIT::AIKIT_DataBuilder *aisoundDataBuilder = nullptr;

    AIKIT::AIKIT_DataBuilder *esrDataBuilder = nullptr;

    AudioCapture *ivwAudioCapture = nullptr;

    AudioCapture *esrAudioCapture = nullptr;

    AudioCapture *isrAudioCapture = nullptr;

    AudioRenderer *audioRender = nullptr;

    std::atomic_bool isEnableEsr = false;

    bool isInit = false;

    std::atomic_bool isDisabled = false;

    std::atomic_bool isLogin = false;

    std::atomic_int isrAudioStatus = 0;

    std::string isrResult = "";

    const char *isrSessionID = nullptr;

    int epStatus; // 写入音频数据状态

    int recStatus; // 识别音频数据状态

    AIKIT_DataStatus status = AIKIT_DataOnce;

    std::vector<short> sourceDatas;

    std::vector<short> renderDatas;

  public:
    static VoiceHelper &
    share();

    /// 语音唤醒能力ID
    const std::string ivwAbility = "e867a88f2";

    /// 语音命令词识别能力ID
    const std::string esrAbility = "e75f07b62";

    /// 语音合成能力ID
    const std::string aisoundAbility = "ece9d3c90";

    /// SDK能力集合
    const std::string abilities = "e867a88f2;ece9d3c90";

    bool
    init(
        const std::shared_ptr<FlutterApi> flutterApi,
        const std::shared_ptr<const std::string> bundlePath
    );

    bool
    init();

    void
    disable();

    bool
    waiting4awakening(
        const std::shared_ptr<const std::string> audioCaptureName
    );

    bool
    waiting4speaking(
        const std::shared_ptr<const std::string> audioRenderName,
        const double volume
    );

    bool
    speakingText(
        const std::shared_ptr<const std::string> text
    );

    bool
    waiting4offlineCommand(
        const std::shared_ptr<const std::string> audioCaptureName
    );

    bool
    waiting4onlineCommand(
        const std::shared_ptr<const std::string> audioCaptureName
    );

    void
    startRecognize();
};

} // namespace MK