#ifndef DEVICE_FFI_H
#define DEVICE_FFI_H

#include <dart_api_dl.h>
#include <pulse/introspect.h>
#include <stdbool.h>

#include "types/attributes_ffi.h"
#include "types/types_ffi.h"
#include "types/vector_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 音频设备
struct AudioDevice {
    /// 是否为默认设备
    bool isDefault;

    /// 设备索引
    uint32_t index;

    /// 设备名称
    char *name;

    /// 设备描述
    char *description;

    /// 设备音量
    pa_cvolume volume;

    /// 设备静音
    int mute;
};

/// 视频采集设备
struct VideoCaptureDevice {
    /// 设备索引
    int index;

    /// 设备路径
    char *path;

    /// 设备名称
    char *name;

    /// true 为 USB 扩展, false 为内置PCIE设备
    bool isUsbExtend;
};

/// 主机设备磁盘空间信息
struct HostDeviceSpaceInfo {
    /// 总容量
    uint64_t capacity;

    /// 总剩余空间
    uint64_t free;

    /// 当前用户配额下剩余空间
    uint64_t available;
};

/// 主机设备磁盘空间信息引用类型
typedef struct HostDeviceSpaceInfo *HostDeviceSpaceInfoRef;

/// 获取主机运行环境平台信息
/// 返回值为静态变量不需要 [malloc.free]
ATTRIBUTES const char *
getHostDevicePlatform();

/// 获取主机指定磁盘空间信息
/// - [path] 磁盘路径
/// 返回值需要 [malloc.free]
ATTRIBUTES HostDeviceSpaceInfoRef
getHostDeviceSpaceInfo(const char *const path);

/// 获取所有视频采集设备
/// 返回向量引用, 需要手动释, 元素类型为 <char *>
ATTRIBUTES VectorRef
getVideoCaptureDevices();

/// 添加视频采集设备监听端口
/// - [port] 监听端口
ATTRIBUTES void
addMonitorPortForVideoCaptureDevices(Dart_Port_DL port);

/// 移除视频采集设备监听端口
/// - [port] 监听端口
ATTRIBUTES void
removeMonitorPortForVideoCaptureDevices(Dart_Port_DL port);

/// 添加音频设备监听端口
/// - [port] 监听端口
ATTRIBUTES void
addMonitorPortForAudioDevices(Dart_Port_DL port);

/// 移除音频设备监听端口
/// - [port] 监听端口
ATTRIBUTES void
removeMonitorPortForAudioDevices(Dart_Port_DL port);

ATTRIBUTES void
setAudioSourceVolume(const char *const name, const int volume);

ATTRIBUTES void
setAudioSinkVolume(const char *const name, const int volume);

ATTRIBUTES void
setDeviceIsOnline(const bool isOnline);

ATTRIBUTES bool
getDeviceIsOnline();

#ifdef __cplusplus
}
#endif

#endif // DEVICE_FFI_H
