#ifndef MONITOR_FFI_H
#define MONITOR_FFI_H

#include <stdbool.h>
#include <stdint.h>

#include "types/attributes_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 监视器引用类型
typedef void *MonitorRef;

/// 创建监视器
/// 返回监视器引用
ATTRIBUTES MonitorRef
monitorCreate();

/// 释放监视器
/// - [monitor] 监视器引用
ATTRIBUTES void
monitorDestroy(MonitorRef monitor);

/// 绑定视频捕获和纹理渲染器
/// - [monitor] 监视器引用
/// - [capture] 视频捕获引用
/// - [renderer] 纹理渲染器引用
/// - [autoStart] 是否自动开始
ATTRIBUTES void
monitorBind(
    MonitorRef monitor,
    void *capture,
    void *renderer,
    bool autoStart
);

/// 开始监视
/// - [monitor] 监视器引用
ATTRIBUTES void
monitorStart(MonitorRef monitor);

/// 停止监视
/// - [monitor] 监视器引用
ATTRIBUTES void
monitorStop(MonitorRef monitor);

#ifdef __cplusplus
}
#endif

#endif // MONITOR_FFI_H
