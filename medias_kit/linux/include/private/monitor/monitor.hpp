#pragma once

#include <atomic>

#include "device/device_ffi.h"
#include "renderer/texture_renderer.hpp"
#include "stream&capture/video_capture.hpp"

namespace MK {

class Monitor {
  private:
    std::atomic_bool pause = false;

    VideoCapture *capture;

    TextureRenderer *renderer;

  public:
    Monitor();

    ~Monitor();

    void
    bind(
        VideoCapture *const capture,
        TextureRenderer *const renderer,
        const bool autoStart
    );

    void
    start();

    void
    stop();
};
} // namespace MK