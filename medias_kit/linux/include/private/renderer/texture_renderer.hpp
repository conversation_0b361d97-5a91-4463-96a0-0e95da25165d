#pragma once

#include <cstdint>
#include <flutter_linux/flutter_linux.h>
#include <mutex>
#include <string>

#include "medias_kit_plugin_private.hpp"

namespace MK {

#define TEXTURE_GL_TYPE (texture_gl_get_type())

G_DECLARE_FINAL_TYPE(TextureGL, texture_gl, TEXTURE_GL, TEXTURE_GL, FlTextureGL)

#define TEXTURE_GL(obj) \
    (G_TYPE_CHECK_INSTANCE_CAST((obj), texture_gl_get_type(), TextureGL))

class TextureRenderer {

    friend gboolean
    texture_gl_populate_texture(
        FlTextureGL *texture,
        uint32_t *target,
        uint32_t *name,
        uint32_t *width,
        uint32_t *height,
        GError **error
    );

    const MediasKitPlugin &plugin;

    const int width;

    const int height;

    uint8_t *const imageBuffer;

    GdkGLContext *glContext = nullptr;

    TextureGL *texture = nullptr;

    int textureId;

    std::mutex mtx;

  public:
    TextureRenderer(
        const MediasKitPlugin &plugin,
        const int width,
        const int height
    );

    ~TextureRenderer();

    bool
    init();

    void
    renderFrame(const uint8_t *frame, const long frameLen);

    /**
     * @brief 返回纹理对象的地址, 供 Flutter Texture 组件绑定
     */
    int64_t
    getTexture() const;
};
} // namespace MK