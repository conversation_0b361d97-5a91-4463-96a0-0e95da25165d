#pragma once

#include <atomic>
#include <condition_variable>
#include <cstring>
#include <map>
#include <memory>
#include <mutex>
#include <pulse/simple.h>
#include <queue>
#include <string>
#include <thread>

namespace MK {
class AudioRenderer {

  public:
    struct AudioFrame {
        const char *const data;

        const int size;

        AudioFrame(const char *data, int size)
            : data(new char[size]()), size(size) {
            auto target = const_cast<char *>(this->data);
            memcpy(target, data, size);
        }

        ~AudioFrame() {
            delete[] data;
        }
    };

  private:
    std::thread loopThread;

    std::atomic_bool isMarkFree = false;

    std::mutex mtx;

    std::mutex breakMtx;

    std::shared_ptr<const std::string> renderName;

    pa_simple *paSimple = nullptr;

    const int sampleRate;

    const bool enableLargeBuffer;

    float volume;

    bool isMuted;

    std::queue<std::shared_ptr<AudioFrame>> frames;

    void
    adjustVolume(void *data, int size, float volume);

  public:
    AudioRenderer(
        const std::shared_ptr<const std::string> renderName,
        const float volume = 1.0f,
        const int sampleRate = 48000,
        const bool enableLargeBuffer = false,
        const bool isMuted = false
    );

    ~AudioRenderer();

    bool
    init(
        const std::shared_ptr<const std::string> renderName = std::make_shared<std::string>()
    );

    void
    setVolume(float volume);

    void
    setMute(bool mute);

    std::shared_ptr<const std::string>
    getRenderName();

    void
    pushFrame(
        const std::shared_ptr<AudioFrame> frame,
        const unsigned int maxBufferLen = 5
    );
};

} // namespace MK
