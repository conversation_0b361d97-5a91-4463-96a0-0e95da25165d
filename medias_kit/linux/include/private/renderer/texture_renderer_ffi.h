#ifndef TEXTURE_RENDERER_FFI_H
#define TEXTURE_RENDERER_FFI_H

#include <stdint.h>

#include "types/attributes_ffi.h"

#ifdef __cplusplus
extern "C" {
#endif

/// 纹理渲染器引用类型
typedef void *TextureRendererRef;

/// 创建纹理渲染器
/// - [pluginId] 插件ID，用于获取对应的 MediasKitPlugin 实例, 取值来源 [MediasKit.pluginId]
/// - [width] 纹理宽度
/// - [height] 纹理高度
/// 返回纹理渲染器引用
ATTRIBUTES TextureRendererRef
textureRendererCreate(
    const char *pluginId,
    int width,
    int height
);

/// 释放纹理渲染器
/// - [renderer] 纹理渲染器引用
ATTRIBUTES void
textureRendererDestroy(TextureRendererRef renderer);

/// 渲染一帧数据
/// - [renderer] 纹理渲染器引用
/// - [frame] 帧数据
/// - [frameLen] 帧数据长度
ATTRIBUTES void
textureRendererRenderFrame(
    TextureRendererRef renderer,
    const uint8_t *const frame,
    long frameLen
);

/// 获取纹理ID
/// - [renderer] 纹理渲染器引用
/// 返回纹理ID，可用于Flutter的Texture组件
ATTRIBUTES int64_t
textureRendererGetTextureId(TextureRendererRef renderer);

#ifdef __cplusplus
}
#endif

#endif // TEXTURE_RENDERER_FFI_H
