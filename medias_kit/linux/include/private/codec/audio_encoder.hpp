#pragma once

#include <atomic>
#include <condition_variable>
#include <cstdint>
#include <fdk-aac/aacenc_lib.h>
#include <functional>
#include <map>
#include <mutex>
#include <queue>
#include <thread>
#include <vector>

namespace MK {

class AudioEncoder {
  public:
    enum class AudioType {
        local,
        remote
    };

    static int16_t
    clamp(int value, int min, int max) {
        if (value < min) return min;
        if (value > max) return max;
        return value;
    }

    static void
    mixAudio(const std::vector<char> &src, std::vector<char> &dst) {
        assert(src.size() == dst.size());
        auto size = src.size() / 2;
        auto srcData = (int16_t *)src.data();
        auto dstData = (int16_t *)dst.data();
        for (int i = 0; i < size; i++) {
            int temp = srcData[i] + dstData[i] * 0.8;
            dstData[i] = clamp(temp, -32768, 32767);
        }
    }

    typedef std::function<void(const uint8_t *, uint32_t, uint64_t)> CallBack;

    const int sampleRate;

    const int bitrate;

    const int numChannels;

  private:
    std::thread loopThread;

    std::atomic_bool isMarkFree = false;

    std::mutex mainMtx;

    std::mutex remoteMtx;

    std::condition_variable mainCV;

    HANDLE_AACENCODER aacEncoder = nullptr;

    AACENC_InfoStruct info;

    uint8_t *outBuffer = nullptr;

    int64_t pts = 0;

    std::queue<std::vector<char>> localAudioFrames;

    std::queue<std::vector<char>> remoteAudioFrames;

    const CallBack didEncode;

  public:
    AudioEncoder(
        const int sampleRate,
        const int bitrate,
        const int numChannels,
        const CallBack didEncode
    );

    ~AudioEncoder();

    bool
    init();

    bool
    encode(
        char *frame,
        long frameLen,
        const AudioType type = AudioType::local
    );
};

} // namespace MK