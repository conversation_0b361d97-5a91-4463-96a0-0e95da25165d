#pragma once

#include <functional>

#include "mw_venc/mw_venc.h"
#include "types/types_ffi.h"

namespace MK {
class VideoEncoder {
  public:
    typedef std::function<void(const uint8_t *, uint32_t, mw_venc_frame_info_t *)> CallBack;

  private:
    mw_venc_handle_t handle = nullptr;

    int64_t pts = 0;

    const CallBack didEncode;

    static void
    onEncodeCallback(
        void *param,
        const uint8_t *package,
        uint32_t packageLen,
        mw_venc_frame_info_t *packageInfo
    );

  public:
    const PixelFormat pixelFormat;

    const int width;

    const int height;

    const int framerate;

    const int bitrate;

    VideoEncoder(
        const PixelFormat pixelFormat,
        const int width,
        const int height,
        const int framerate,
        const int bitrate,
        const CallBack didEncode
    );

    ~VideoEncoder();

    bool
    init();

    bool
    encode(const uint8_t *const frame, long frameLen);
};
} // namespace MK