// #pragma once

// #include <functional>
// #include <map>
// #include <memory>
// #include <mutex>

// #include "LibMWCapture/MWCapture.h"

// namespace MK {

// #define VIDEO_FRAME_NUM 4

// class videoLowLatencyCapture {
//   public:
//     typedef std::function<void(unsigned char *, long)> CallBack;

//   private:
//     const std::shared_ptr<const std::string> identifier;

//     std::shared_ptr<const std::string> name;

//     int frameIndex = 0;

//     int frameCount = 0;

//     unsigned int minStride;

//     unsigned int frameSize;

//     unsigned char *frames[VIDEO_FRAME_NUM];

//     long long times[VIDEO_FRAME_NUM];

//     void *channel = nullptr;

//     MWCAP_PTR captureEvent = 0;

//     MWCAP_PTR timerEvent = 0;

//     HTIMER timer = 0;

//     std::mutex mtx;

//     const CallBack didCapture;

//   public:
//     const int width;

//     const int height;

//     const int framerate;

//     videoLowLatencyCapture(
//         const std::shared_ptr<const std::string> identifier,
//         const std::shared_ptr<const std::string> name,
//         const int width,
//         const int height,
//         const int framerate,
//         const CallBack didCapture
//     );

//     ~videoLowLatencyCapture();

//     bool
//     init();

//     const std::string &
//     getName() const;
// };
// } // namespace MK