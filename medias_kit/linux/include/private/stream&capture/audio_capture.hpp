#pragma once

#include <atomic>
#include <condition_variable>
#include <cstdint>
#include <map>
#include <mutex>
#include <pulse/simple.h>

#include "types/audio_frame.hpp"

namespace MK {

class AudioCapture {
  public:
    typedef std::function<void(char *, long)> CallBack;

  private:
    std::thread loopThread;

    std::atomic_bool isMarkFree = false;

    std::mutex mtx;

    std::mutex breakMtx;

    std::shared_ptr<const std::string> captureName;

    pa_simple *paSimple = nullptr;

    const CallBack didCapture;

    char *captureBuffer = nullptr;

    uint32_t bufferLen;

  public:
    const int numChannels;

    const uint32_t periodTime = 10; // 豪秒

    const uint32_t sampleRate;

    AudioCapture(
        const std::shared_ptr<const std::string> captureName,
        const int sampleRate,
        const int numChannels,
        const CallBack didCapture
    );

    ~AudioCapture();

    bool
    init(
        const std::shared_ptr<const std::string> captureName = std::make_shared<std::string>()
    );

    std::shared_ptr<const std::string>
    getCaptureName();
};

} // namespace MK