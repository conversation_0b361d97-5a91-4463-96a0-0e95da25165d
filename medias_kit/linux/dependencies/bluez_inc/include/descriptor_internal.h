/*
 *   Copyright (c) 2022 <PERSON><PERSON><PERSON>
 *
 *   Permission is hereby granted, free of charge, to any person obtaining a copy
 *   of this software and associated documentation files (the "Software"), to deal
 *   in the Software without restriction, including without limitation the rights
 *   to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *   copies of the Software, and to permit persons to whom the Software is
 *   furnished to do so, subject to the following conditions:
 *
 *   The above copyright notice and this permission notice shall be included in all
 *   copies or substantial portions of the Software.
 *
 *   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *   SOFTWARE.
 *
 */

#ifndef BINC_DESCRIPTOR_INTERNAL_H
#define BINC_DESCRIPTOR_INTERNAL_H

#include "descriptor.h"

Descriptor *binc_descriptor_create(Device *device, const char *path);

void binc_descriptor_free(Descriptor *descriptor);

void binc_descriptor_set_read_cb(Descriptor *descriptor, OnDescReadCallback callback);

void binc_descriptor_set_write_cb(Descriptor *descriptor, OnDescWriteCallback callback);

void binc_descriptor_set_uuid(Descriptor *descriptor, const char *uuid);

void binc_descriptor_set_char_path(Descriptor *descriptor, const char *path);

void binc_descriptor_set_char(Descriptor *descriptor, Characteristic *characteristic);

void binc_descriptor_set_flags(Descriptor *descriptor, GList *flags);

const char *binc_descriptor_get_char_path(const Descriptor *descriptor);

#endif //BINC_DESCRIPTOR_INTERNAL_H
