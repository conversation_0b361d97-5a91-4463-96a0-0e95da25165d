header-y += aosl_alloca.h
header-y += aosl_mm.h
header-y += aosl_atomic.h
header-y += aosl_angel.h
header-y += aosl_defs.h
header-y += aosl_errno.h
header-y += aosl_async.h
header-y += aosl_socket.h
header-y += aosl_fcntl.h
header-y += aosl_file.h
header-$(CONFIG_ENABLE_HTTP) += aosl_http.h
header-y += aosl_input.h
header-y += aosl_integer_wrappings.h
header-$(call |,$(CONFIG_ANDROID),$(CONFIG_ENABLE_JAVA)) += aosl_jni.h
header-y += aosl_list.h
header-y += aosl_log.h
header-$(CONFIG_MARSHALLING) += aosl_marshalling.h
header-y += aosl_mpq.h
header-y += aosl_mpq_fd.h
header-y += aosl_mpq_net.h
header-y += aosl_mpq_timer.h
header-y += aosl_mpqp.h
header-$(CONFIG_PSB) += aosl_psb.h
header-y += aosl_rbtree.h
header-y += aosl_ref.h
header-y += aosl_route.h
header-y += aosl_task.h
header-y += aosl_thread.h
header-y += aosl_time.h
header-y += aosl_types.h
header-y += aosl_utils.h
header-y += aosl_version.h
header-y += aosl_so.h
header-y += aosl_module.h
header-y += aosl_os.h
header-y += aosl_pmdf.h
header-y += aosl_profile.h
header-y += aosl_kobj.h
header-y += aosl_value.h
header-y += aosl_display.h
header-y += aosl_poll.h
header-y += aosl_ares.h
header-y += aosl_dq.h
header-y += cpp/
header-$(CONFIG_XNU) += objc/
header-$(CONFIG_AUDIO) += aosl_audio.h
header-$(CONFIG_VIDEO) += aosl_video.h
header-$(CONFIG_XDUMP) += aosl_xdump.h
