/**
 *
 * Agora Real Time Engagement
 * Copyright (c) 2024 Agora IO. All rights reserved.
 *
 */
#pragma once

#include "rte_base/c/common.h"                          // IWYU pragma: export
#include "rte_base/c/bridge.h"                         // IWYU pragma: export
#include "rte_base/c/channel.h"                        // IWYU pragma: export
#include "rte_base/c/device/audio.h"                   // IWYU pragma: export
#include "rte_base/c/device/audio_device_manager.h"    // IWYU pragma: export
#include "rte_base/c/device/video.h"                   // IWYU pragma: export
#include "rte_base/c/device/video_device_manager.h"    // IWYU pragma: export
#include "rte_base/c/log.h"                            // IWYU pragma: export
#include "rte_base/c/observer.h"                       // IWYU pragma: export
#include "rte_base/c/options.h"                        // IWYU pragma: export
#include "rte_base/c/c_error.h"                        // IWYU pragma: export
#include "rte_base/c/c_player.h"                       // IWYU pragma: export
#include "rte_base/c/c_rte.h"                          // IWYU pragma: export
#include "rte_base/c/handle.h"                         // IWYU pragma: export
#include "rte_base/c/stream/local_cdn_stream.h"        // IWYU pragma: export
#include "rte_base/c/stream/local_realtime_stream.h"   // IWYU pragma: export
#include "rte_base/c/stream/remote_cdn_stream.h"       // IWYU pragma: export
#include "rte_base/c/stream/remote_realtime_stream.h"  // IWYU pragma: export
#include "rte_base/c/stream/stream.h"                  // IWYU pragma: export
#include "rte_base/c/track/camera_video_track.h"       // IWYU pragma: export
#include "rte_base/c/track/canvas.h"                   // IWYU pragma: export
#include "rte_base/c/track/layout.h"                   // IWYU pragma: export
#include "rte_base/c/track/local_audio_track.h"        // IWYU pragma: export
#include "rte_base/c/track/local_video_track.h"        // IWYU pragma: export
#include "rte_base/c/track/mic_audio_track.h"          // IWYU pragma: export
#include "rte_base/c/track/mixed_video_track.h"        // IWYU pragma: export
#include "rte_base/c/track/remote_audio_track.h"       // IWYU pragma: export
#include "rte_base/c/track/remote_track.h"             // IWYU pragma: export
#include "rte_base/c/track/remote_video_track.h"       // IWYU pragma: export
#include "rte_base/c/track/screen_video_track.h"       // IWYU pragma: export
#include "rte_base/c/track/track.h"                    // IWYU pragma: export
#include "rte_base/c/track/video_track.h"              // IWYU pragma: export
#include "rte_base/c/track/view.h"                     // IWYU pragma: export
#include "rte_base/c/user/local_user.h"                // IWYU pragma: export
#include "rte_base/c/user/remote_user.h"               // IWYU pragma: export
#include "rte_base/c/user/user.h"                      // IWYU pragma: export
#include "rte_base/c/utils/buf.h"                      // IWYU pragma: export
#include "rte_base/c/utils/frame.h"                    // IWYU pragma: export
