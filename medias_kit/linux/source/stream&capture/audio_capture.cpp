#include "stream&capture/audio_capture.hpp"

#include <memory>
#include <mutex>
#include <pulse/error.h>
#include <string>
#include <utility>

#include "utils/logger.hpp"

namespace MK {

AudioCapture::AudioCapture(
    const std::shared_ptr<const std::string> captureName,
    const int sampleRate,
    const int numChannels,
    const CallBack didCapture
)
    : captureName(captureName),
      didCapture(didCapture),
      numChannels(numChannels),
      sampleRate(sampleRate) {
}

AudioCapture::~AudioCapture() {
    isMarkFree = true;
    if (loopThread.joinable()) {
        loopThread.join();
    }
    if (paSimple) {
        int error;
        pa_simple_drain(paSimple, &error);
        pa_simple_free(paSimple);
    }
    if (captureBuffer) {
        delete[] captureBuffer;
    }
    LOG_I("{}", "Memory free!");
}

bool
AudioCapture::init(const std::shared_ptr<const std::string> captureName) {
    if (isMarkFree) {
        return false;
    }
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    if (paSimple && captureName == this->captureName) {
        return true;
    }
    if (captureName && !captureName->empty()) {
        this->captureName = captureName;
        if (paSimple) {
            int error;
            pa_simple_drain(paSimple, &error);
            pa_simple_free(paSimple);
            paSimple = nullptr;
        }
    }

    if (paSimple) {
        return true;
    }

    if (!captureBuffer) {
        bufferLen = numChannels * sampleRate * 2 * periodTime / 1000;
        captureBuffer = new char[bufferLen]();
    }

    const pa_sample_spec ss = {
        .format = PA_SAMPLE_S16LE,
        .rate = sampleRate,
        .channels = uint8_t(numChannels),
    };

    // 更小的 buffer, 可以有效的降低延迟
    const pa_buffer_attr attr = {
        .maxlength = (uint32_t)-1,
        .tlength = bufferLen,
        .prebuf = 0,
        .minreq = bufferLen,
        .fragsize = bufferLen,
    };

captureRenew:
    auto realName = *this->captureName == "default" ? nullptr : this->captureName->c_str();
    int error;
    paSimple = pa_simple_new(
        nullptr,
        "surgsmart-input",
        PA_STREAM_RECORD,
        realName,
        "record",
        &ss,
        nullptr,
        &attr,
        &error
    );
    if (!paSimple) {
        LOG_E("pa_simple_new failure! message: {}", pa_strerror(error));
        if (realName) {
            this->captureName = std::make_shared<std::string>("default");
            goto captureRenew;
        }
        return false;
    }

    if (!captureName || captureName->empty()) {
        std::thread task([&]() {
            while (true) {
                std::unique_lock<std::mutex> breakLock(breakMtx);
                std::lock_guard<std::mutex> lock(mtx);
                breakLock.unlock();
                if (isMarkFree) {
                    break;
                }
                int error;
                auto ret = pa_simple_read(paSimple, captureBuffer, bufferLen, &error);
                if (ret < 0) {
                    LOG_E("{}", pa_strerror(error));
                    continue;
                }
                ret = pa_simple_get_latency(paSimple, &error);
                if (ret > 2000) {
                    ret = pa_simple_flush(paSimple, &error);
                    if (ret < 0) {
                        LOG_E("{}", pa_strerror(error));
                    }
                }
                didCapture(captureBuffer, bufferLen);
            }
            LOG_I("{}", "audio capture thread free!");
        });
        loopThread = std::move(task);
    }
    return true;
}

std::shared_ptr<const std::string>
AudioCapture::getCaptureName() {
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    return captureName;
}

} // namespace MK
