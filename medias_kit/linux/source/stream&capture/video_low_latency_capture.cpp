
// #include "stream&capture/video_low_latency_capture.hpp"

// #include <cstdio>
// #include <cstdlib>
// #include <thread>

// #include "MWFOURCC.h"
// #include "device/device_ffi.h"
// #include "utils/logger.hpp"

// namespace MK {

// videoLowLatencyCapture::videoLowLatencyCapture(
//     const std::shared_ptr<const std::string> identifier,
//     const std::shared_ptr<const std::string> name,
//     const int width,
//     const int height,
//     const int framerate,
//     const CallBack didCapture
// )
//     : identifier(identifier),
//       name(name),
//       didCapture(didCapture),
//       width(width),
//       height(height),
//       framerate(framerate) {}

// videoLowLatencyCapture::~videoLowLatencyCapture() {
//     LOG_I("{}", "Memory free!");
// }

// bool
// videoLowLatencyCapture::init() {
//     minStride = FOURCC_CalcMinStride(MWFOURCC_RGBA, width, 4);
//     frameSize = FOURCC_CalcImageSize(MWFOURCC_RGBA, width, height, minStride);

//     for (auto i = 0; i < VIDEO_FRAME_NUM; i++) {
//         frames[i] = new unsigned char[frameSize]();
//         if (nullptr == frames[i]) {
//             LOG_E("new unsigned char[{}]() failure!", frameSize);
//             return false;
//         }
//     }

//     auto captures = getVideoCaptures();

//     if (captures->size() == 0) {
//         LOG_E("{}", "Not detect any video capture!");
//         return false;
//     }
//     auto index = 0;
//     auto iterator = captures->begin();
//     if (!name) {
//         name = std::make_unique<std::string>(iterator->data());
//     } else {
//         while (iterator != captures->end()) {
//             if (iterator->data() == *name) {
//                 break;
//             }
//             iterator++;
//             index += 1;
//         }
//         if (index >= captures->size()) {
//             LOG_E("Capture {} not exist!", *name);
//             return false;
//         }
//     }
//     char devicePath[128] = {0};
//     auto ret = MWGetDevicePath(index, devicePath);
//     if (MW_SUCCEEDED != ret) {
//         LOG_E("{}", "MWGetDevicePath failure!");
//         return false;
//     }
//     channel = MWOpenChannelByPath(devicePath);
//     if (!channel) {
//         LOG_E("{}", "MWOpenChannelByPath failure!");
//         return false;
//     }
//     // MWCAP_CHANNEL_INFO channelInfo;
//     // if (MW_SUCCEEDED != MWGetChannelInfo(channel, &channelInfo)) {
//     //     LOG_E("{}", "MWGetChannelInfo failure!");
//     //     return false;
//     // }

//     captureEvent = MWCreateEvent();
//     if (!captureEvent) {
//         LOG_E("{}", "MWCreateEvent captureEvent failure!");
//         return false;
//     }

//     ret = MWStartVideoCapture(channel, captureEvent);
//     if (MW_SUCCEEDED != ret) {
//         LOG_E("{}", "MWStartVideoCapture failure!");
//         return false;
//     }

//     MWCAP_VIDEO_SIGNAL_STATUS status;
//     ret = MWGetVideoSignalStatus(channel, &status);
//     if (MW_SUCCEEDED != ret) {
//         LOG_E("{}", "MWGetVideoSignalStatus failure!");
//         return false;
//     }

//     timerEvent = MWCreateEvent();
//     if (!timerEvent) {
//         LOG_E("{}", " MWCreateEvent timerEvent failure!");
//         return false;
//     }

//     timer = MWRegisterTimer(channel, timerEvent);
//     if (timer == 0) {
//         LOG_E("{}", "MWRegisterTimer failure!");
//         return false;
//     }

//     // 绑定缓冲区
//     for (auto i = 0; i < VIDEO_FRAME_NUM; i++) {
//         MWPinVideoBuffer(channel, (MWCAP_PTR)frames[i], frameSize);
//     }

//     auto thread = std::thread([this]() {
//         LONGLONG beginTime = 0;
//         auto ret = MWGetDeviceTime(channel, &beginTime);
//         if (MW_SUCCEEDED != ret) {
//             LOG_E("{}", "MWGetDeviceTime failure!");
//         }
//         while (true) {
//             beginTime += 10000000 / framerate;
//             ret = MWScheduleTimer(channel, timer, beginTime);
//             if (MW_SUCCEEDED != ret) {
//                 continue;
//             }

//             if (MWWaitEvent(timerEvent, 1000) <= 0) {
//                 LOG_E(
//                     "{}",
//                     "before MWCaptureVideoFrameToVirtualAddressEx "
//                     "MWWaitEvent failure, timer error or timeout!"
//                 );
//                 break;
//             }

//             MWCAP_VIDEO_BUFFER_INFO bufferInfo;
//             ret = MWGetVideoBufferInfo(channel, &bufferInfo);
//             if (MW_SUCCEEDED != ret) {
//                 continue;
//             }

//             MWCAP_VIDEO_FRAME_INFO frameInfo;
//             ret = MWGetVideoFrameInfo(
//                 channel, bufferInfo.iNewestBufferedFullFrame, &frameInfo
//             );
//             if (MW_SUCCEEDED != ret) {
//                 continue;
//             }
//             std::lock_guard<std::mutex> lock(mtx);
//             printf("00 >>>>>>>>>>>>>>>>>>>>> %d\n", frameCount);

//             ret = MWCaptureVideoFrameToVirtualAddressEx(
//                 channel,
//                 bufferInfo.iNewestBufferedFullFrame,
//                 frames[frameIndex],
//                 frameSize,
//                 minStride,
//                 0,
//                 0,
//                 MWFOURCC_RGBA,
//                 width,
//                 height,
//                 0,
//                 0,
//                 0,
//                 0,
//                 0,
//                 100,
//                 0,
//                 100,
//                 0,
//                 MWCAP_VIDEO_DEINTERLACE_BLEND,
//                 MWCAP_VIDEO_ASPECT_RATIO_CROPPING,
//                 0,
//                 0,
//                 0,
//                 0,
//                 MWCAP_VIDEO_COLOR_FORMAT_UNKNOWN,
//                 MWCAP_VIDEO_QUANTIZATION_UNKNOWN,
//                 MWCAP_VIDEO_SATURATION_UNKNOWN
//             );
//             if (MW_SUCCEEDED != ret) {
//                 continue;
//             }

//             if (MWWaitEvent(timerEvent, 1000) <= 0) {
//                 LOG_E(
//                     "{}",
//                     "behind MWCaptureVideoFrameToVirtualAddressEx "
//                     "MWWaitEvent failure, timer error or timeout!"
//                 );
//                 break;
//             }
//             // didCapture(frames[frameIndex], frameSize);

//             MWCAP_VIDEO_CAPTURE_STATUS captureStatus;
//             MWGetVideoCaptureStatus(channel, &captureStatus);
//             MWGetDeviceTime(channel, &times[frameIndex]);
//             times[frameIndex] /= 10000;
//             frameCount += 1;
//             if (frameIndex >= VIDEO_FRAME_NUM) {
//                 frameIndex = 0;
//             }
//         }
//     });

//     return true;
// }

// const std::string &
// videoLowLatencyCapture::getName() const {
//     return *name;
// }

// } // namespace MK
