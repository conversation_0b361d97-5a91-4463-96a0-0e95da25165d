#include "stream&capture/video_capture.hpp"

#include <cstdio>
#include <cstring>
#include <map>
#include <memory>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <string>
#include <thread>
#include <vector>

#include "LibMWCapture/MWCapture.h"
#include "MWFOURCC.h"
#include "device/device_ffi.h"
#include "utils/logger.hpp"

namespace MK {

class InternalCapture {

    std::mutex mutex;

    std::map<const std::string, std::set<VideoCapture *>> observers;

    std::map<const std::string, cv::VideoCapture> captures;

    InternalCapture() = default;

    ~InternalCapture() = default;

  public:
    static InternalCapture &
    share() {
        static InternalCapture capture;
        return capture;
    }

    bool
    addObserver(VideoCapture *observer) {
        assert(observer != nullptr);
        auto devicePath = observer->getVideoDevice().path;
        std::lock_guard<std::mutex> lock(mutex);
        observers[devicePath].insert(observer);

        auto &capture = captures[devicePath];
        if (!capture.isOpened()) {
            auto isSuccess = capture.open(*devicePath, cv::CAP_V4L2);
            if (!isSuccess) {
                LOG_E("Open {} failed!", *devicePath);
                return false;
            }
            capture.set(cv::CAP_PROP_FRAME_WIDTH, observer->width);
            capture.set(cv::CAP_PROP_FRAME_HEIGHT, observer->height);
            capture.set(cv::CAP_PROP_FPS, observer->framerate);
            capture.set(cv::CAP_PROP_FORMAT, CV_8UC4);

            std::thread([this, devicePath]() {
                auto &capture = captures[devicePath];

                cv::Mat originFrame;
                cv::Mat rgbaFrame;
                cv::Mat i420Frame;
                while (true) {
                    try {
                        capture >> originFrame;

                        cv::cvtColor(originFrame, rgbaFrame, cv::COLOR_BGRA2RGBA);
                        auto rgbaFrameLen = rgbaFrame.total() * rgbaFrame.elemSize();

                        cv::cvtColor(originFrame, i420Frame, cv::COLOR_BGRA2YUV_I420);
                        auto i420FrameLen = i420Frame.total() * i420Frame.elemSize();

                        std::lock_guard<std::mutex> lock(mutex);
                        auto &observers = this->observers[devicePath];
                        if (observers.empty()) {
                            capture.release();
                            // captures.erase(*devicePath);
                            break;
                        }
                        for (auto observer : observers) {
                            if (observer->pixelFormat == PixelFormat::pf_rgba) {
                                VideoCapture::onCaptureCallback(
                                    rgbaFrame.data,
                                    rgbaFrameLen,
                                    observer
                                );
                            } else {
                                VideoCapture::onCaptureCallback(
                                    i420Frame.data,
                                    i420FrameLen,
                                    observer
                                );
                            }
                        }
                    } catch (cv::Exception error) {
                        LOG_E("{}", error.what());
                        break;
                    }
                }
                LOG_I("{}", "OpenCV capture thread free!");
            }).detach();
        }
        return true;
    }

    void
    removeObserver(VideoCapture *observer) {
        std::lock_guard<std::mutex> lock(mutex);
        observers[observer->videoDevice->path].erase(observer);
    }
};

void
VideoCapture::onCaptureCallback(
    unsigned char *frame,
    long frameLen,
    void *param
) {
    auto capture = (VideoCapture *)param;
    std::lock_guard<std::mutex> lock(capture->mtx);
    if (capture->channel) {
        MWCAP_VIDEO_SIGNAL_STATUS signalStatus;
        MWGetVideoSignalStatus(capture->channel, &signalStatus);
        capture->_hasSignal = signalStatus.state > MWCAP_VIDEO_SIGNAL_UNSUPPORTED;
    }
    if (capture->frameCallBack) {
        capture->frameCallBack(capture->pixelFormat, frame, frameLen);
    }
}

VideoCapture::VideoCapture(
    const std::shared_ptr<const VideoDevice> videoDevice,
    const PixelFormat pixelFormat,
    const int width,
    const int height,
    const int framerate
)
    : videoDevice(videoDevice),
      pixelFormat(pixelFormat),
      width(width),
      height(height),
      framerate(framerate) {
}

VideoCapture::~VideoCapture() {
    isInit = false;
    bindFrameCallBack(nullptr);
    if (videoDevice->isUsbExtend) {
        InternalCapture::share().removeObserver(this);
    }
    if (handle) {
        MWDestoryVideoCapture(handle);
    }
    if (channel) {
        MWCloseChannel(channel);
    }
    LOG_I("{}", "Memory free!");
}

bool
VideoCapture::init() {
    if (isInit) {
        return isInit;
    }
    if (videoDevice->isUsbExtend) {
        return isInit = InternalCapture::share().addObserver(this);
    } else {
        auto captures = getDeviceVideoCaptures();
        if (!videoDevice->path) {
            if (captures == 0) {
                LOG_E("{}", "Not detect any video capture!");
                return false;
            }
            auto index = 0;
            std::string devName;
            // for (auto &name : *captures) {
            //     if (name.find("Pro Capture") != std::string::npos) {
            //         break;
            //     }
            //     index++;
            //     devName = name;
            // }
            char devicePath[128] = {0};
            if (MW_SUCCEEDED != MWGetDevicePath(index, devicePath)) {
                LOG_E("{}", "MWGetDevicePath failure!");
                return isInit = false;
            }

            auto device = std::make_shared<VideoDevice>();
            // device->name = std::make_shared<const std::string>(devName);
            // device->path = std::make_shared<const std::string>(devicePath);
            device->isUsbExtend = true;
            // device->index = std::stoi(device->path->substr(10));
            videoDevice = device;
            channel = MWOpenChannelByPath(devicePath);
        } else {
            // channel = MWOpenChannelByPath(videoDevice->path->c_str());
        }
        if (!channel) {
            LOG_E("{}", "MWOpenChannelByPath failure!");
            return isInit = false;
        }
        handle = MWCreateVideoCapture(
            channel,
            width,
            height,
            pixelFormat == PixelFormat::pf_rgba ? MWFOURCC_RGBA : MWFOURCC_NV12,
            10000000 / framerate,
            onCaptureCallback,
            this
        );
        if (!handle) {
            LOG_E("{}", "MWCreateVideoCapture failure!");
            return isInit = false;
        }
    }
    return isInit = true;
}

void
VideoCapture::bindFrameCallBack(const FrameCallBack frameCallBack) {
    this->frameCallBack = frameCallBack;
}

const VideoDevice &
VideoCapture::getVideoDevice() const {
    return *videoDevice;
}

bool
VideoCapture::hasSignal() const {
    return _hasSignal;
}

} // namespace MK
