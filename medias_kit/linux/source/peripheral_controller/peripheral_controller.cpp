
#include "peripheral_controller/peripheral_controller.hpp"

#include <cstdlib>
#include <cstring>
#include <fstream>
#include <glib.h>
#include <glibconfig.h>
#include <iostream>
#include <ostream>
#include <signal.h>
#include <stdio.h>
#include <string>
#include <system_error>
#include <thread>
#include <unistd.h>

#include "adapter.h"
#include "advertisement.h"
#include "agent.h"
#include "application.h"
#include "device.h"
#include "logger.h"
#include "parser.h"
#include "utility.h"
#include "utils/logger.hpp"

namespace MK {

#define TAG "Main"
#define HTS_SERVICE_UUID "00001809-0000-1000-8000-00805f9b34fb"
#define TEMPERATURE_CHAR_UUID "00002a1c-0000-1000-8000-00805f9b34fb"
#define CUD_CHAR "00002901-0000-1000-8000-00805f9b34fb"

GDBusConnection *dbusConnection = NULL;
GMainLoop *loop = NULL;
Adapter *default_adapter = NULL;
Advertisement *advertisement = NULL;
Application *app = NULL;
std::shared_ptr<FlutterApi> flutterMethod = nullptr;
std::shared_ptr<const std::string> callBackIdentifier = nullptr;
Device *lastDevice = NULL;
gboolean peripheralStarted = false;
char *advertName = NULL;

void
call_connection_state(gboolean state) {
    auto arguments = fl_value_new_map();
    fl_value_set_string_take(
        arguments,
        "identifier",
        fl_value_new_string(callBackIdentifier->c_str())
    );
    fl_value_set_string_take(arguments, "connected", fl_value_new_bool(state));
    if (state && lastDevice != NULL) {
        const char *connectionName = binc_device_get_name(lastDevice);
        if (connectionName != NULL) {
            fl_value_set_string_take(
                arguments, "deviceName", fl_value_new_string(connectionName)
            );
        } else {
            const char *connectionAlias = binc_device_get_alias(lastDevice);
            if (connectionAlias != NULL) {
                fl_value_set_string_take(
                    arguments,
                    "deviceName",
                    fl_value_new_string(connectionAlias)
                );
            }
        }

        const char *connectionAddress = binc_device_get_address(lastDevice);
        if (connectionAddress != NULL) {
            fl_value_set_string_take(
                arguments,
                "deviceAddress",
                fl_value_new_string(connectionAddress)
            );
        }
    }

    if (default_adapter != NULL) {
        const char *adapterAddress = binc_adapter_get_address(default_adapter);
        if (adapterAddress != NULL) {
            fl_value_set_string_take(
                arguments, "adapterAddress", fl_value_new_string(adapterAddress)
            );
        }
        fl_value_set_string_take(
            arguments,
            "adapterState",
            fl_value_new_bool(binc_adapter_get_powered_state(default_adapter))
        );
    }
    if (advertName != NULL) {
        fl_value_set_string_take(
            arguments, "adapterName", fl_value_new_string(advertName)
        );
    }

    auto event =
        static_cast<int64_t>(PeripheralController::ReverseEvent::state);
    auto method = new std::string("PeripheralController.");
    method->append(std::to_string(event));
    flutterMethod->call(method, arguments);
}

void
on_powered_state_changed(Adapter *adapter, gboolean state) {
    log_debug(
        TAG,
        "powered '%s' (%s)",
        state ? "on" : "off",
        binc_adapter_get_path(adapter)
    );
    call_connection_state(state ? lastDevice != NULL : false);
    if (peripheralStarted && advertisement != NULL) {
        if (state) {
            binc_adapter_start_advertising(adapter, advertisement);
        } else {
            binc_adapter_stop_advertising(adapter, advertisement);
        }
    }
}

void
on_central_state_changed(Adapter *adapter, Device *device) {
    char *deviceToString = binc_device_to_string(device);
    log_debug(TAG, deviceToString);
    g_free(deviceToString);

    ConnectionState state = binc_device_get_connection_state(device);
    if (state == BINC_CONNECTED) {
        lastDevice = device;
        call_connection_state(true);
        binc_adapter_stop_advertising(adapter, advertisement);
    } else if (state == BINC_DISCONNECTED) {
        lastDevice = NULL;
        call_connection_state(false);
        binc_adapter_start_advertising(adapter, advertisement);
    }
}

const char *
on_local_char_read(
    const Application *application,
    const char *address,
    const char *service_uuid,
    const char *char_uuid
) {
    if (g_str_equal(service_uuid, HTS_SERVICE_UUID) &&
        g_str_equal(char_uuid, TEMPERATURE_CHAR_UUID)) {
    }
    return BLUEZ_ERROR_REJECTED;
}

const char *
on_local_char_write(
    const Application *application,
    const char *address,
    const char *service_uuid,
    const char *char_uuid,
    GByteArray *byteArray
) {
    log_debug(TAG, "on on_local_char_write data length %d", byteArray->len);
    if (g_str_equal(service_uuid, HTS_SERVICE_UUID) &&
        g_str_equal(char_uuid, TEMPERATURE_CHAR_UUID)) {

        auto arguments = fl_value_new_map();
        fl_value_set_string_take(
            arguments,
            "identifier",
            fl_value_new_string(callBackIdentifier->c_str())
        );

        char *str = (char *)malloc(byteArray->len + 1);
        memcpy(str, byteArray->data, byteArray->len);
        str[byteArray->len] = '\0';
        fl_value_set_string_take(arguments, "data", fl_value_new_string(str));

        auto event =
            static_cast<int64_t>(PeripheralController::ReverseEvent::accept);
        auto method = new std::string("PeripheralController.");
        method->append(std::to_string(event));
        flutterMethod->call(method, arguments);
        free(str);
        return NULL;
    }
    return BLUEZ_ERROR_REJECTED;
}

void
on_local_char_start_notify(
    const Application *application,
    const char *service_uuid,
    const char *char_uuid
) {
    log_debug(TAG, "on start notify");
    if (g_str_equal(service_uuid, HTS_SERVICE_UUID) &&
        g_str_equal(char_uuid, TEMPERATURE_CHAR_UUID)) {
        const guint8 bytes[] = "0A-1-1-0-";
        GByteArray *byteArray = g_byte_array_sized_new(sizeof(bytes));
        g_byte_array_append(byteArray, bytes, sizeof(bytes));
        binc_application_notify(
            application, service_uuid, char_uuid, byteArray
        );
        g_byte_array_free(byteArray, TRUE);
    }
}

void
on_local_char_stop_notify(
    const Application *application,
    const char *service_uuid,
    const char *char_uuid
) {
    log_debug(TAG, "on stop notify");
}

gboolean
callback(gpointer data) {
    if (app != NULL) {
        binc_adapter_unregister_application(default_adapter, app);
        binc_application_free(app);
        app = NULL;
    }

    if (advertisement != NULL) {
        binc_adapter_stop_advertising(default_adapter, advertisement);
        binc_advertisement_free(advertisement);
    }

    if (default_adapter != NULL) {
        binc_adapter_free(default_adapter);
        default_adapter = NULL;
    }

    g_main_loop_quit((GMainLoop *)data);
    return FALSE;
}

void
cleanup_handler(int signo) {
    if (signo == SIGINT) {
        log_error(TAG, "received SIGINT");
        callback(loop);
    }
}

gboolean
startAdvertising() {
    // Get a DBus connection
    dbusConnection = g_bus_get_sync(G_BUS_TYPE_SYSTEM, NULL, NULL);

    // Setup handler for CTRL+C
    if (signal(SIGINT, cleanup_handler) == SIG_ERR)
        log_error(TAG, "can't catch SIGINT");

    // Setup mainloop
    loop = g_main_loop_new(NULL, FALSE);

    // Get the default default_adapter
    default_adapter = binc_adapter_get_default(dbusConnection);

    if (default_adapter != NULL) {
        // Make sure the adapter is on
        binc_adapter_set_powered_state_cb(
            default_adapter, &on_powered_state_changed
        );
        if (!binc_adapter_get_powered_state(default_adapter)) {
            log_debug("MAIN", "adapter_get_powered_state off");
            binc_adapter_power_on(default_adapter);
            peripheralStarted = false;
            call_connection_state(false);

            return false;
        }

        // Setup remote central connection state callback
        binc_adapter_set_remote_central_cb(
            default_adapter, &on_central_state_changed
        );

        // Setup advertisement
        GPtrArray *adv_service_uuids = g_ptr_array_new();
        g_ptr_array_add(adv_service_uuids, (void *)HTS_SERVICE_UUID);

        advertisement = binc_advertisement_create();
        char hostname[256];
        if (gethostname(hostname, sizeof(hostname)) != 0) {
            strcpy(hostname, "SurgSmart-default");
        }
        //记录每次广播出去的名称，gethostname可能存在获取不到情况
        advertName = hostname;
        binc_advertisement_set_local_name(advertisement, hostname);
        binc_advertisement_set_services(advertisement, adv_service_uuids);
        g_ptr_array_free(adv_service_uuids, TRUE);
        binc_adapter_start_advertising(default_adapter, advertisement);

        // Start application
        app = binc_create_application(default_adapter);
        binc_application_add_service(app, HTS_SERVICE_UUID);
        binc_application_add_characteristic(
            app,
            HTS_SERVICE_UUID,
            TEMPERATURE_CHAR_UUID,
            GATT_CHR_PROP_INDICATE | GATT_CHR_PROP_READ | GATT_CHR_PROP_WRITE
        );
        binc_application_add_descriptor(
            app,
            HTS_SERVICE_UUID,
            TEMPERATURE_CHAR_UUID,
            CUD_CHAR,
            GATT_CHR_PROP_READ | GATT_CHR_PROP_WRITE
        );

        const guint8 cud[] = "SurgSmart BLE Control";
        GByteArray *cudArray = g_byte_array_sized_new(sizeof(cud));
        g_byte_array_append(cudArray, cud, sizeof(cud));
        binc_application_set_desc_value(
            app, HTS_SERVICE_UUID, TEMPERATURE_CHAR_UUID, CUD_CHAR, cudArray
        );

        binc_application_set_char_read_cb(app, &on_local_char_read);
        binc_application_set_char_write_cb(app, &on_local_char_write);
        binc_application_set_char_start_notify_cb(
            app, &on_local_char_start_notify
        );
        binc_application_set_char_stop_notify_cb(
            app, &on_local_char_stop_notify
        );
        binc_adapter_register_application(default_adapter, app);
        peripheralStarted = true;
        call_connection_state(false);

    } else {
        log_debug("MAIN", "No default_adapter found");
        peripheralStarted = false;
        call_connection_state(false);

        return false;
    }

    // Bail out after some time
    // g_timeout_add_seconds(600, callback, loop);

    // Start the mainloop
    g_main_loop_run(loop);

    // Clean up mainloop
    g_main_loop_unref(loop);

    // Disconnect from DBus
    if (dbusConnection != NULL &&
        !g_dbus_connection_is_closed(dbusConnection)) {
        g_dbus_connection_close_sync(dbusConnection, NULL, NULL);
    }
    g_object_unref(dbusConnection);
    peripheralStarted = false;

    return true;
}

PeripheralController::PeripheralController(
    const std::shared_ptr<FlutterApi> flutterApi,
    const std::shared_ptr<const std::string> identifier
)
    : flutterApi(flutterApi), identifier(identifier) {
    flutterMethod = flutterApi;
    callBackIdentifier = identifier;
}

PeripheralController::~PeripheralController() {
    isInit = false;
}

bool
PeripheralController::init() {

    return isInit = true;
}

void
PeripheralController::start() {
    if (dbusConnection == NULL) {
        peripheralStarted = false;
    }
    if (peripheralStarted) {
        call_connection_state(lastDevice != NULL);
        return;
    }

    // c线程启动
    peripheralStarted = true;
    std::thread([]() {
        startAdvertising();
    }).detach();
}

void
PeripheralController::stop() {
    if (lastDevice == NULL) {
        return;
    }
    binc_device_disconnect(lastDevice);
}

void
PeripheralController::notify(const std::shared_ptr<const std::string> cmd
) const {
    int length = std::strlen((char *)cmd->c_str());

    GByteArray *cudArray = g_byte_array_sized_new(length);

    g_byte_array_append(cudArray, (guint8 *)cmd->c_str(), length);

    binc_application_notify(
        app, HTS_SERVICE_UUID, TEMPERATURE_CHAR_UUID, cudArray
    );
    g_byte_array_free(cudArray, TRUE);
}

void
PeripheralController::dispose() {
    peripheralStarted = false;
    isInit = false;
    stop();

    cleanup_handler(SIGINT);
}

} // namespace MK
