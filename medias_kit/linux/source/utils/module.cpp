#include "utils/module.hpp"

#include <memory>

namespace MK {

Module::Module(const std::string &name, const int event)
    : name(name), event(event) {}

const std::shared_ptr<Module>
Module::parse(const std::string &source) {
    auto position = source.find('.', 0);
    auto name = source.substr(0, position);
    auto eventID = source.substr(position + 1, source.size());
    auto event = std::stoi(eventID);
    return std::make_shared<Module>(name, event);
}

} // namespace MK
