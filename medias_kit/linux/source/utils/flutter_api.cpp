#include "utils/flutter_api.hpp"

#include "utils/logger.hpp"
#include <cstdio>

namespace MK {

FlutterApi::UserData::UserData(
    FlMethodChannel *const channel,
    std::string *const method,
    FlValue *const args
)
    : channel(channel), method(method), arguments(args) {}

FlutterApi::UserData::~UserData() {
    if (method) {
        delete method;
    }
}

FlutterApi::FlutterApi(FlMethodChannel *const channel)
    : channel(channel) {}

FlutterApi::~FlutterApi() {
    LOG_I("{}", "Memory free!");
}

void
FlutterApi::call(std::string *const method, FlValue *const arguments) const {
    auto userData = new UserData(channel, method, arguments);

    auto context = g_main_context_default();
    g_main_context_invoke(
        context,
        [](gpointer userData) -> gboolean {
            auto data = static_cast<UserData *>(userData);
            g_autoptr(FlValue) arguments = data->arguments;
            fl_method_channel_invoke_method(
                data->channel,
                data->method->c_str(),
                arguments,
                nullptr,
                nullptr,
                nullptr
            );
            delete data;
            return FALSE;
        },
        userData
    );
}

void
FlutterApi::safeCall(std::string *const method, FlValue *const arguments) {
    throw "Unimplemented!";
}

} // namespace MK
