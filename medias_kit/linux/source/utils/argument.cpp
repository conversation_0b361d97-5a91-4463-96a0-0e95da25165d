#include "utils/argument.hpp"
#include "utils/logger.hpp"
#include <cassert>
#include <cstdio>
#include <string>

namespace MK {

Argument::Argument(FlValue *value)
    : value(value) {
    assert(
        fl_value_get_type(value) == FL_VALUE_TYPE_MAP ||
        fl_value_get_type(value) == FL_VALUE_TYPE_NULL
    );
}

const std::shared_ptr<const std::string>
Argument::getString(const std::string key) const {
    auto lookup = fl_value_lookup_string(value, key.c_str());
    if (!lookup) {
        LOG_E("Flutter parameter not found! Field = {}", key);
        return nullptr;
    }
    auto type = fl_value_get_type(lookup);
    if (type != FL_VALUE_TYPE_STRING) {
        if (type != FL_VALUE_TYPE_NULL) {
            LOG_E("Flutter parameter type error! Field = {}", key);
        }
        return nullptr;
    }
    auto result = fl_value_get_string(lookup);
    return std::make_shared<std::string>(result);
}

const int64_t
Argument::getInt(const std::string key) const {
    auto lookup = fl_value_lookup_string(value, key.c_str());
    if (!lookup) {
        LOG_E("Flutter parameter not found! Field = {}", key);
        return 0;
    }
    auto type = fl_value_get_type(lookup);
    if (type != FL_VALUE_TYPE_INT) {
        if (type != FL_VALUE_TYPE_NULL) {
            LOG_E("Flutter parameter type error! Field = {}", key);
        }
        return 0;
    }
    auto result = fl_value_get_int(lookup);
    return result;
}

const double
Argument::getFloat(const std::string key) const {
    auto lookup = fl_value_lookup_string(value, key.c_str());
    if (!lookup) {
        LOG_E("Flutter parameter not found! Field = {}", key);
        return 0;
    }
    auto type = fl_value_get_type(lookup);
    if (type != FL_VALUE_TYPE_FLOAT) {
        if (type != FL_VALUE_TYPE_NULL) {
            LOG_E("Flutter parameter type error! Field = {}", key);
        }
        return 0;
    }
    auto result = fl_value_get_float(lookup);
    return result;
}

const bool
Argument::getBool(const std::string key) const {
    auto lookup = fl_value_lookup_string(value, key.c_str());
    if (!lookup) {
        LOG_E("Flutter parameter not found! Field = {}", key);
        return false;
    }
    auto type = fl_value_get_type(lookup);
    if (type != FL_VALUE_TYPE_BOOL) {
        if (type != FL_VALUE_TYPE_NULL) {
            LOG_E("Flutter parameter type error! Field = {}", key);
        }
        return false;
    }
    auto result = fl_value_get_bool(lookup);
    return result;
}

const std::shared_ptr<Argument>
Argument::getSubArgument(const std::string key) const {
    auto lookup = fl_value_lookup_string(value, key.c_str());
    if (!lookup) {
        LOG_E("Flutter parameter not found! Field = {}", key);
        return nullptr;
    }
    auto type = fl_value_get_type(lookup);
    if (type != FL_VALUE_TYPE_MAP) {
        if (type != FL_VALUE_TYPE_NULL) {
            LOG_E("Flutter parameter type error! Field = {}", key);
        }
        return nullptr;
    }
    auto result = std::make_shared<Argument>(lookup);
    return result;
}

} // namespace MK
