#include "utils/logger.hpp"

#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/spdlog.h>
#include <string>

namespace MK {

using namespace spdlog;

const Logger &Log = Logger::instance();

Logger::Logger() {
    if (std::string("Debug") == CURRENT_BUILD_TYPE) {
        auto consoleSink =
            std::make_shared<sinks::stdout_color_sink_mt>(color_mode::always);
        consoleSink->set_color(level::trace, consoleSink->white);
        consoleSink->set_color(level::debug, consoleSink->cyan);
        consoleSink->set_color(level::info, consoleSink->blue);
        consoleSink->set_color(level::warn, consoleSink->yellow);
        consoleSink->set_color(level::err, consoleSink->red);
        consoleSink->set_color(level::critical, consoleSink->red_bold);
        consoleSink->set_level(level::trace);
        consoleSink->set_pattern(
            "medias_kit: %^[%Y-%m-%d %H:%M:%S.%e] [%l] [thread %t] %v%$"
        );

        logger = std::make_shared<spdlog::logger>("stdLog", consoleSink);
    } else {
        const char *home = std::getenv("HOME");
        auto baseFileName = std::string(home) +
                            "/Documents/v202310/medias-kit.logs/medias-kit.log";
        auto fileSink = std::make_shared<sinks::rotating_file_sink_mt>(
            baseFileName, 10 * 1024 * 1024, 7
        );
        fileSink->set_level(level::info);
        fileSink->set_pattern(
            R"({"timestamp":"%Y-%m-%d %H:%M:%S.%e", "level":"%l", "thread_id":%t, %v})"
        );

        logger = std::make_shared<spdlog::logger>("fileLog", fileSink);
    }

    logger->set_level(level::trace);
    logger->flush_on(level::warn);

    register_logger(logger);
    flush_every(std::chrono::seconds(2));
}

const Logger &
Logger::instance() {
    static Logger instance;
    return instance;
}

} // namespace MK
