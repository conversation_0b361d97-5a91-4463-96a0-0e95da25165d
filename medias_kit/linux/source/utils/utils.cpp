#include "utils/utils.hpp"

#include <chrono>
#include <iomanip>
#include <memory>
#include <sstream>
#include <string>

#include "utils/logger.hpp"

namespace MK {

std::shared_ptr<std::string>
Utils::int2hex(const int value, const int minWidth) {
    std::stringstream ss;
    ss << "0x" << std::setw(minWidth) << std::setfill('0') << std::hex << value;
    return std::make_shared<std::string>(ss.str());
}

int
Utils::getCodeRunTime(
    const std::function<void()> func,
    const std::string &label,
    const bool enableLog
) {
    auto start = std::chrono::high_resolution_clock::now();
    func();
    auto end = std::chrono::high_resolution_clock::now();
    auto duration =
        std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
            .count();
    if (enableLog) {
        LOG_I("{} Run time: {}ms", label, duration);
    }
    return duration;
}

} // namespace MK