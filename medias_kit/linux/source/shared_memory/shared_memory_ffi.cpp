#include "shared_memory/shared_memory_ffi.h"
#include "shared_memory/shared_memory.hpp"
#include "utils/logger.hpp"

SharedMemoryRef
sharedMemoryOpen(const char *key, size_t size) {
    MK::LOG_E(">>>>>>>>>>>{}", "");
    auto *shm = new MK::SharedMemory(key, size);
    if (!shm->openOrCreate()) {
        delete shm;
        return nullptr;
    }
    return shm;
}

void
sharedMemoryFree(void *handle) {
    if (handle) {
        auto *shm = static_cast<MK::SharedMemory *>(handle);
        shm->destroy();
        delete shm;
    }
}

bool
sharedMemoryWrite(SharedMemoryRef memory, const void *data, size_t size) {
    if (!memory) return false;
    auto *shm = static_cast<MK::SharedMemory *>(memory);
    return shm->write(data, size);
}

bool
sharedMemoryRead(SharedMemoryRef memory, void *buffer, size_t size) {
    if (!memory) return false;
    auto *shm = static_cast<MK::SharedMemory *>(memory);
    return shm->read(buffer, size);
}