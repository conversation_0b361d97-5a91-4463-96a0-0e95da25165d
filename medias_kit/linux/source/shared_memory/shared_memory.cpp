#include "shared_memory/shared_memory.hpp"
#include <cstring>
#include <errno.h>
#include <fstream>
#include <string>
#include <sys/shm.h>

#include "utils/logger.hpp"

namespace MK {

SharedMemory::SharedMemory(const std::string &key, const size_t size)
    : shmId(-1), shmAddr(nullptr), shmSize(size), shmKey(key) {
    LOG_D("SharedMemory created with key: {}, size: {}", key, size);
}

SharedMemory::~SharedMemory() {
    detach();
}

bool
SharedMemory::openOrCreate() {
    if (access(shmKey.c_str(), F_OK) == -1) {
        LOG_D("Key file not found, creating: {}", shmKey);
        std::ofstream file(shmKey.c_str());
        if (!file) {
            LOG_E("Failed to create key file: {}, error: {}", shm<PERSON>ey, strerror(errno));
            return false;
        }
        file.close();

        if (chmod(shmKey.c_str(), 0666) == -1) {
            LOG_E("Failed to chmod key file: {}, error: {}", shmKey, strerror(errno));
            return false;
        }
    }

    key_t key = ftok(shmKey.c_str(), 1);
    if (key == -1) {
        LOG_E("ftok failed for key file: {}, error: {}", shmKey, strerror(errno));
        return false;
    }
    LOG_D("Generated IPC key: {} for file: {}", key, shmKey);

    shmId = shmget(key, shmSize, 0666);
    if (shmId != -1) {
        LOG_D("Shared memory already exists with id: {}", shmId);
        return attach();
    }
    shmId = shmget(key, shmSize, IPC_CREAT | 0666);
    if (shmId == -1) {
        LOG_E("shmget failed with key: {}, size: {}, error: {}", key, shmSize, strerror(errno));
        return false;
    }
    LOG_D("Created shared memory with id: {}", shmId);
    return attach();
}

bool
SharedMemory::attach() {
    if (shmAddr != nullptr) {
        LOG_D("{}", "Already attached to shared memory");
        return true;
    }

    shmAddr = shmat(shmId, nullptr, 0);
    if (shmAddr == (void *)-1) {
        LOG_E("shmat failed for id: {}, error: {}", shmId, strerror(errno));
        shmAddr = nullptr;
        return false;
    }
    LOG_D("Attached to shared memory at address: {}", (void *)shmAddr);

    return true;
}

bool
SharedMemory::detach() {
    if (shmAddr == nullptr) {
        LOG_D("{}", "Already detached from shared memory");
        return true;
    }

    if (shmdt(shmAddr) == -1) {
        LOG_E("shmdt failed for address: {}, error: {}", (void *)shmAddr, strerror(errno));
        return false;
    }
    LOG_D("Detached from shared memory at address: {}", (void *)shmAddr);

    shmAddr = nullptr;
    return true;
}

bool
SharedMemory::destroy() {
    if (shmId == -1) {
        LOG_D("{}", "No shared memory to destroy");
        return true;
    }

    if (shmctl(shmId, IPC_RMID, nullptr) == -1) {
        LOG_E("shmctl failed for id: {}, error: {}", shmId, strerror(errno));
        return false;
    }
    LOG_D("Destroyed shared memory with id: {}", shmId);

    shmId = -1;
    return true;
}

bool
SharedMemory::write(const void *data, size_t size) {
    if (shmAddr == nullptr) {
        LOG_E("{}", "Cannot write: shared memory not attached");
        return false;
    }

    if (size > shmSize) {
        LOG_E("Write failed: data size ({}) exceeds shared memory size ({})", size, shmSize);
        return false;
    }

    memcpy(shmAddr, data, size);
    LOG_D("Wrote {} bytes to shared memory", size);
    return true;
}

bool
SharedMemory::read(void *buffer, size_t size) {
    if (shmAddr == nullptr) {
        LOG_E("{}", "Cannot read: shared memory not attached");
        return false;
    }

    if (size > shmSize) {
        LOG_E("Read failed: requested size ({}) exceeds shared memory size ({})", size, shmSize);
        return false;
    }

    memcpy(buffer, shmAddr, size);
    LOG_D("Read {} bytes from shared memory", size);
    return true;
}

} // namespace MK