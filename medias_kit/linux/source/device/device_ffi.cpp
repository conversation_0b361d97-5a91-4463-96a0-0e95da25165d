#include "device/device_ffi.h"

#include <atomic>
#include <cassert>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <flutter_linux/flutter_linux.h>
#include <functional>
#include <iomanip>
#include <libudev.h>
#include <linux/videodev2.h>
#include <map>
#include <memory>
#include <pulse/context.h>
#include <pulse/def.h>
#include <pulse/introspect.h>
#include <pulse/operation.h>
#include <pulse/subscribe.h>
#include <pulse/thread-mainloop.h>
#include <pulse/volume.h>
#include <string>
#include <sys/ioctl.h>
#include <sys/utsname.h>
#include <thread>
#include <tuple>
#include <unistd.h>
#include <vector>

#include "LibMWCapture/MWCapture.h"
#include "dart_api_dl.h"
#include "dart_native_api.h"
#include "utils/logger.hpp"

std::atomic_bool _isOnline = false;

// Dart_CObject obj;
// obj.type = Dart_CObject_Type::Dart_CObject_kInt64;
// obj.value.as_int64 = 0;
// Dart_PostCObject_DL(nullptr, obj);

const char *
getHostDevicePlatform() {
    static utsname unameData;
    if (strlen(unameData.version) == 0) {
        auto ret = uname(&unameData);
        if (ret != 0) {
            MK::LOG_E("{}", "uname failure!");
            return nullptr;
        }
    }
    return unameData.version;
}

HostDeviceSpaceInfoRef
getHostDeviceSpaceInfo(const char *const path) {
    assert(path);
    std::error_code errCode;
    auto spaceInfo = std::filesystem::space(path, errCode);
    if (errCode) {
        MK::LOG_E("{}", errCode.message());
        return nullptr;
    }
    auto info = (HostDeviceSpaceInfo *)malloc(sizeof(HostDeviceSpaceInfo));
    info->capacity = spaceInfo.capacity;
    info->free = spaceInfo.free;
    info->available = spaceInfo.available;
    return info;
}

VectorRef
getVideoCaptureDevices() {
    auto names = new std::vector<char *>();
    static bool isInit = false;
    if (!isInit && !MWCaptureInitInstance()) {
        MK::LOG_E("{}", "MWCaptureInitInstance failure!");
        return nullptr;
    }
    isInit = true;
    if (MW_SUCCEEDED != MWRefreshDevice()) {
        MK::LOG_E("{}", "MWRefreshDevice failure!");
        return nullptr;
    }
    auto count = MWGetChannelCount();
    MWCAP_CHANNEL_INFO info;
    for (int i = 0; i < count; i++) {
        if (MW_SUCCEEDED != MWGetChannelInfoByIndex(i, &info)) {
            MK::LOG_E("MWGetChannelInfoByIndex {} failure!", i);
        } else {
            char *name = (char *)calloc(1, strlen(info.szProductName));
            memcpy(name, info.szProductName, strlen(info.szProductName));
            names->push_back(name);
        }
    }
    return names;
}

pa_threaded_mainloop *mainloop = nullptr;
pa_context *context = nullptr;
std::map<std::string, std::unique_ptr<AudioDevice>> sinkDevices;
std::map<std::string, std::unique_ptr<AudioDevice>> sourceDevices;
std::string defaultSinkName;
std::string defaultSourceName;

std::vector<std::shared_ptr<VideoCaptureDevice>> videoDevices;

void
syncAudioDevices(
    std::string *method,
    std::map<std::string, std::unique_ptr<AudioDevice>> &devices
) {
    auto arguments = fl_value_new_list();

    for (auto &device : devices) {
        auto map = fl_value_new_map();

        auto &value = device.second;

        fl_value_set_string_take(
            map, "isDefault", fl_value_new_bool(value->isDefault)
        );
        fl_value_set_string_take(map, "index", fl_value_new_int(value->index));
        fl_value_set_string_take(
            map, "name", fl_value_new_string(value->name)
        );
        fl_value_set_string_take(
            map, "description", fl_value_new_string(value->description)
        );
        int volumeCount = 0;
        for (int i = 0; i < value->volume.channels; i++) {
            volumeCount += value->volume.values[i];
        }
        auto volume = volumeCount / value->volume.channels;
        auto percent = volume * 100 / PA_VOLUME_NORM;
        fl_value_set_string_take(map, "volume", fl_value_new_int(percent));
        fl_value_set_string_take(map, "mute", fl_value_new_bool(value->mute));

        fl_value_append_take(arguments, map);
    }
    // audioFlutterApi->call(method, arguments);
}

void
sinkInfoCallback(
    pa_context *context,
    const pa_sink_info *sinkInfo,
    int eol,
    void *userdata
) {
    // if (eol) {
    //     auto event =
    //         static_cast<int64_t>(ReverseEvent::audioSinkChanged);
    //     auto method = new std::string("Device.");
    //     method->append(std::to_string(event));
    //     syncAudioDevices(method, sinkDevices);
    //     LOG_I("{}", "Synced audio sinks!");
    //     if (audioSinksCallback) {
    //         audioSinksCallback(sinkDevices);
    //     }
    //     return;
    // }
    // auto &sink = sinkDevices[sinkInfo->name] =
    //     std::make_unique<AudioDevice>();
    // sink->index = sinkInfo->index;
    // sink->name = std::make_shared<std::string>(sinkInfo->name);
    // sink->description = std::make_shared<std::string>(sinkInfo->description);
    // sink->volume = sinkInfo->volume;
    // sink->mute = sinkInfo->mute;
    // sink->isDefault = *sink->name == defaultSinkName;
}

void
sourceInfoCallback(
    pa_context *context,
    const pa_source_info *sourceInfo,
    int eol,
    void *userdata
) {
    if (eol) {
        // auto event =
        //     static_cast<int64_t>(ReverseEvent::audioSourceChanged);
        // auto method = new std::string("Device.");
        // method->append(std::to_string(event));
        // syncAudioDevices(method, sourceDevices);
        // LOG_I("{}", "Synced audio sources!");
        // if (audioSourcesCallback) {
        //     audioSourcesCallback(sourceDevices);
        // }
        return;
    }

    if (!sourceInfo->active_port) return;
    // if (strcmp("analog-input-rear-mic", sourceInfo->active_port->name) == 0 &&
    //     PA_PORT_AVAILABLE_YES != sourceInfo->active_port->available)
    //     return;
    // auto &source = sourceDevices[sourceInfo->name] =
    //     std::make_unique<AudioDevice>();
    // source->index = sourceInfo->index;
    // source->name = std::make_shared<std::string>(sourceInfo->name);
    // source->description =
    //     std::make_shared<std::string>(sourceInfo->description);
    // source->volume = sourceInfo->volume;
    // source->mute = sourceInfo->mute;
    // source->isDefault = *source->name == defaultSourceName;
}

void
serverInfoCallback(
    pa_context *context,
    const pa_server_info *info,
    void *userdata
) {
    int *facility = static_cast<int *>(userdata);
    defaultSinkName = std::string(info->default_sink_name);
    defaultSourceName = std::string(info->default_source_name);

    if (PA_SUBSCRIPTION_EVENT_SINK == *facility) {
        sinkDevices.clear();
        auto sinkOperation =
            pa_context_get_sink_info_list(context, sinkInfoCallback, userdata);
        if (sinkOperation) pa_operation_unref(sinkOperation);
    } else if (PA_SUBSCRIPTION_EVENT_SOURCE == *facility) {
        sourceDevices.clear();
        auto sourceOperation = pa_context_get_source_info_list(
            context, sourceInfoCallback, userdata
        );
        if (sourceOperation) pa_operation_unref(sourceOperation);
    } else {
        sinkDevices.clear();
        auto sinkOperation =
            pa_context_get_sink_info_list(context, sinkInfoCallback, userdata);
        if (sinkOperation) pa_operation_unref(sinkOperation);

        sourceDevices.clear();
        auto sourceOperation = pa_context_get_source_info_list(
            context, sourceInfoCallback, userdata
        );
        if (sourceOperation) pa_operation_unref(sourceOperation);
    }
    delete facility;
}

void
cardInfoCallback(
    pa_context *context,
    const pa_card_info *info,
    int eol,
    void *userdata
) {
    if (eol) {
        return;
    }
}

void
setupSinkCallback(pa_context *context, int success, void *userdata) {}

void
setupSourceCallback(pa_context *context, int success, void *userdata) {}

void
subscribeContextCallback(pa_context *context, int success, void *userdata) {}

void
stateCallback(pa_context *context, void *userdata) {
    pa_context_state_t state = pa_context_get_state(context);
    switch (state) {
    case PA_CONTEXT_READY: {
        auto facility = new int(-1);

        auto serverOperation =
            pa_context_get_server_info(context, serverInfoCallback, facility);
        if (serverOperation) pa_operation_unref(serverOperation);

        auto mask = pa_subscription_mask_t(
            PA_SUBSCRIPTION_MASK_SINK | PA_SUBSCRIPTION_MASK_SOURCE |
            PA_SUBSCRIPTION_MASK_CARD
        );
        auto subscribeOperation = pa_context_subscribe(
            context, mask, subscribeContextCallback, userdata
        );
        if (subscribeOperation) pa_operation_unref(subscribeOperation);
    } break;
    case PA_CONTEXT_FAILED:
    case PA_CONTEXT_TERMINATED:
        MK::LOG_E("{}", "PulseAudio context failed or terminated!");
        break;
    default:
        break;
    }
}

void
subscribeCallback(
    pa_context *context,
    pa_subscription_event_type_t type,
    uint32_t idx,
    void *userdata
) {
    auto operation = type & PA_SUBSCRIPTION_EVENT_TYPE_MASK;

    auto facility = new int();
    *facility = type & PA_SUBSCRIPTION_EVENT_FACILITY_MASK;

    // if (PA_SUBSCRIPTION_EVENT_CARD == facility) {
    //     pa_context_get_card_info_by_index(context, idx, cardInfoCallback, userdata);
    // }

    if (PA_SUBSCRIPTION_EVENT_CHANGE == operation) { // 改变
        auto op =
            pa_context_get_server_info(context, serverInfoCallback, facility);
        if (op) pa_operation_unref(op);
    }
}

int
startMonitoring() {
    mainloop = pa_threaded_mainloop_new();
    auto *mainloop_api = pa_threaded_mainloop_get_api(mainloop);
    context = pa_context_new(mainloop_api, "surgsmart-audio-device-monitor");

    pa_context_set_state_callback(context, stateCallback, nullptr);
    pa_context_set_subscribe_callback(context, subscribeCallback, nullptr);

    pa_threaded_mainloop_start(mainloop);
    pa_threaded_mainloop_lock(mainloop);
    pa_context_connect(context, nullptr, PA_CONTEXT_NOFLAGS, nullptr);
    pa_threaded_mainloop_unlock(mainloop);
    while (true) {
        pa_threaded_mainloop_lock(mainloop);
        pa_context_state_t state = pa_context_get_state(context);
        pa_threaded_mainloop_unlock(mainloop);
        if (PA_CONTEXT_FAILED == state || PA_CONTEXT_TERMINATED == state) {
            break;
        }
        pa_threaded_mainloop_wait(mainloop);
    }
    pa_context_disconnect(context);
    pa_context_unref(context);
    pa_threaded_mainloop_stop(mainloop);
    pa_threaded_mainloop_free(mainloop);

    return 0;
}

void
addMonitorPortForAudioDevices(Dart_Port_DL port) {
    std::thread(startMonitoring).detach();
}

void
setAudioSourceVolume(const char *const name, const int volume) {
    auto userdata = new std::tuple<const std::string, const int>(name, volume);
    pa_threaded_mainloop_lock(mainloop);
    pa_threaded_mainloop_once_unlocked(
        mainloop,
        [](pa_threaded_mainloop *mainloop, void *userdata) {
            auto ptr =
                static_cast<std::tuple<const std::string, const int> *>(userdata
                );
            auto [name, volume] = *ptr;
            auto device = sourceDevices.find(name);
            if (device != sourceDevices.end()) {
                auto percent = volume / 100.0;
                auto oldVolume = device->second->volume;
                pa_cvolume newVolume;
                pa_cvolume_reset(&newVolume, oldVolume.channels);
                pa_cvolume_set(
                    &newVolume, oldVolume.channels, PA_VOLUME_NORM * percent
                );

                auto muteOperation = pa_context_set_source_mute_by_name(
                    context,
                    name.c_str(),
                    volume == 0,
                    setupSourceCallback,
                    nullptr
                );
                if (muteOperation) pa_operation_unref(muteOperation);

                auto volumeOperation = pa_context_set_source_volume_by_name(
                    context,
                    name.c_str(),
                    &newVolume,
                    setupSourceCallback,
                    nullptr
                );
                if (volumeOperation) {
                    MK::LOG_I(
                        "Set source: {}, volume: {}%",
                        *device->second->description,
                        volume
                    );
                    pa_operation_unref(volumeOperation);
                }
            }
            delete ptr;
        },
        userdata
    );
    pa_threaded_mainloop_unlock(mainloop);
}

void
setAudioSinkVolume(const char *const name, const int volume) {
    auto userdata = new std::tuple<const std::string, const int>(name, volume);
    pa_threaded_mainloop_lock(mainloop);
    pa_threaded_mainloop_once_unlocked(
        mainloop,
        [](pa_threaded_mainloop *mainloop, void *userdata) {
            auto ptr =
                static_cast<std::tuple<const std::string, const int> *>(userdata
                );
            auto [name, volume] = *ptr;
            auto device = sinkDevices.find(name);
            if (device != sinkDevices.end()) {
                auto percent = volume / 100.0;
                auto oldVolume = device->second->volume;
                pa_cvolume newVolume;
                pa_cvolume_reset(&newVolume, oldVolume.channels);
                pa_cvolume_set(
                    &newVolume, oldVolume.channels, PA_VOLUME_NORM * percent
                );

                auto muteOperation = pa_context_set_sink_mute_by_name(
                    context,
                    name.c_str(),
                    volume == 0,
                    setupSinkCallback,
                    nullptr
                );
                if (muteOperation) pa_operation_unref(muteOperation);

                auto volumeOperation = pa_context_set_sink_volume_by_name(
                    context,
                    name.c_str(),
                    &newVolume,
                    setupSinkCallback,
                    nullptr
                );
                if (volumeOperation) {
                    MK::LOG_I(
                        "Set sink: {}, volume: {}%",
                        *device->second->description,
                        volume
                    );
                    pa_operation_unref(volumeOperation);
                }
            }
            delete ptr;
        },
        userdata
    );
    pa_threaded_mainloop_unlock(mainloop);
}

std::shared_ptr<VideoCaptureDevice>
generateVideoDevice(const std::string &path) {
    auto fd = open(path.c_str(), O_RDWR);
    if (fd) {
        v4l2_capability cap;
        ioctl(fd, VIDIOC_QUERYCAP, &cap);
        auto isCapture = V4L2_TYPE_IS_CAPTURE(cap.capabilities);
        if (!isCapture) {
            return nullptr;
        }

        v4l2_fmtdesc fmt;
        fmt.index = 0;
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        if (ioctl(fd, VIDIOC_ENUM_FMT, &fmt)) {
            return nullptr;
        }

        auto device = std::make_shared<VideoCaptureDevice>();
        auto index = path.substr(strlen("/dev/video"), path.length());
        device->index = atoi(index.c_str());
        // device->name = std::make_shared<std::string>((char *)cap.card);
        // device->path = std::make_shared<std::string>(path);
        auto busInfo = g_ascii_strdown(
            (gchar *)cap.bus_info, strlen((gchar *)cap.bus_info)
        );
        device->isUsbExtend = strncmp(busInfo, "usb", 3) == 0;
        return device;
    }
    return nullptr;
}

void
listCurrentVideoDevices(udev *udev) {
    videoDevices.clear();

    auto enumerate = udev_enumerate_new(udev);
    udev_enumerate_add_match_subsystem(enumerate, "video4linux");
    udev_enumerate_scan_devices(enumerate);
    auto devices = udev_enumerate_get_list_entry(enumerate);
    udev_list_entry *entry;
    udev_list_entry_foreach(entry, devices) {
        auto entryPath = udev_list_entry_get_name(entry);
        auto device = udev_device_new_from_syspath(udev, entryPath);
        auto path = udev_device_get_devnode(device);
        auto videoDevice = generateVideoDevice(path);
        if (videoDevice) {
            videoDevices.push_back(videoDevice);
        }
        udev_device_unref(device);
    };
    udev_enumerate_unref(enumerate);

    auto list = fl_value_new_list();
    for (auto device : videoDevices) {
        auto map = fl_value_new_map();
        fl_value_set_string_take(map, "index", fl_value_new_int(device->index));
        fl_value_set_string_take(
            map, "name", fl_value_new_string(device->name)
        );
        fl_value_set_string_take(
            map, "path", fl_value_new_string(device->path)
        );
        fl_value_set_string_take(
            map, "isUsbExtend", fl_value_new_bool(device->isUsbExtend)
        );

        fl_value_append_take(list, map);
    }

    // auto event = static_cast<int64_t>(ReverseEvent::videoSourceChanged);
    // auto method = new std::string("Device.");
    // method->append(std::to_string(event));
    // videoFlutterApi->call(method, list);
}

void
addMonitorPortForVideoCaptureDevices(Dart_Port_DL port) {
    std::thread([]() {
        auto udev = udev_new();
        if (!udev) {
            MK::LOG_E("{}", "udev_new failed!");
            return;
        }

        // 枚举现有设备
        listCurrentVideoDevices(udev);

        // 监听插拔事件
        auto monitor = udev_monitor_new_from_netlink(udev, "udev");
        if (!monitor) {
            MK::LOG_E("{}", "udev_monitor_new_from_netlink failed!");
            udev_unref(udev);
            return;
        }
        udev_monitor_filter_add_match_subsystem_devtype(
            monitor, "video4linux", nullptr
        );
        udev_monitor_enable_receiving(monitor);
        auto fd = udev_monitor_get_fd(monitor);

        while (true) {
            fd_set fds;
            FD_ZERO(&fds);
            FD_SET(fd, &fds);
            auto ret = select(fd + 1, &fds, nullptr, nullptr, nullptr);
            if (ret > 0 && FD_ISSET(fd, &fds)) {
                auto device = udev_monitor_receive_device(monitor);
                if (device) {
                    auto action = udev_device_get_action(device);
                    if (action && strcmp(action, "add") == 0) {
                        listCurrentVideoDevices(udev);
                    } else if (action && strcmp(action, "remove") == 0) {
                        listCurrentVideoDevices(udev);
                    }
                    udev_device_unref(device);
                }
            } else {
                MK::LOG_E("{}", "Video device monitoring failure!");
                break;
            }
        }
        udev_monitor_unref(monitor);
        udev_unref(udev);
    }).detach();
}

void
setDeviceIsOnline(const bool isOnline) {
    _isOnline = isOnline;
}

bool
getDeviceIsOnline() {
    return _isOnline;
}
