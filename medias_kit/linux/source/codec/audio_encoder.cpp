#include "codec/audio_encoder.hpp"

#include <algorithm>
#include <cassert>
#include <cstdint>
#include <mutex>

#include "utils/logger.hpp"

namespace MK {

void
pushFrames(
    const bool needEmplace,
    const int standFrameLen,
    std::queue<std::vector<char>> &audioFrames,
    const char *const frame,
    const int frameLen
) {
    if (needEmplace) {
        audioFrames.emplace();
        audioFrames.back().reserve(standFrameLen);
    }
    auto &audioFrame = audioFrames.back();
    if (audioFrame.empty()) {
        audioFrame.assign(frame, frame + frameLen);
    } else {
        auto remainLen = standFrameLen - audioFrame.size();
        if (remainLen == 0) {
            return pushFrames(
                true,
                standFrameLen,
                audioFrames,
                frame,
                frameLen
            );
        }
        auto result = audioFrame.end();
        if (remainLen >= frameLen) {
            audioFrame.resize(audioFrame.size() + frameLen);
            std::copy(frame, frame + frameLen, result);
        } else {
            audioFrame.resize(standFrameLen);
            std::copy(frame, frame + remainLen, result);
            pushFrames(
                true,
                standFrameLen,
                audioFrames,
                frame + remainLen,
                frameLen - remainLen
            );
        }
    }
}

AudioEncoder::AudioEncoder(
    const int sampleRate,
    const int bitrate,
    const int numChannels,
    const CallBack didEncode
)
    : sampleRate(sampleRate),
      bitrate(bitrate),
      numChannels(numChannels),
      didEncode(didEncode) {
}

AudioEncoder::~AudioEncoder() {
    isMarkFree = true;
    mainCV.notify_one();
    if (loopThread.joinable()) {
        loopThread.join();
    }
    if (aacEncoder) {
        aacEncClose(&aacEncoder);
    }
    if (outBuffer) {
        delete[] outBuffer;
    }
    LOG_I("{}", "Memory free!");
}

bool
AudioEncoder::init() {
    if (outBuffer) {
        return true;
    }
    auto ret = aacEncOpen(&aacEncoder, 0, 0);
    if (AACENC_OK != ret) {
        LOG_E("aacEncOpen failed, ret: {}", ret);
        return false;
    }

    ret = aacEncoder_SetParam(aacEncoder, AACENC_AOT, AOT_AAC_LC);
    if (AACENC_OK != ret) {
        LOG_E("aacEncoder_SetParam AACENC_AOT failed, ret: {}", ret);
        return false;
    }
    ret = aacEncoder_SetParam(aacEncoder, AACENC_CHANNELMODE, MODE_1);
    if (AACENC_OK != ret) {
        LOG_E("aacEncoder_SetParam AACENC_CHANNELMODE failed, ret: {}", ret);
        return false;
    }
    ret = aacEncoder_SetParam(aacEncoder, AACENC_SAMPLERATE, sampleRate); // 48000
    if (AACENC_OK != ret) {
        LOG_E("aacEncoder_SetParam AACENC_SAMPLERATE failed, ret: {}", ret);
        return false;
    }
    ret = aacEncoder_SetParam(aacEncoder, AACENC_BITRATE, bitrate); // 96000
    if (AACENC_OK != ret) {
        LOG_E("aacEncoder_SetParam AACENC_BITRATE failed, ret: {}", ret);
        return false;
    }
    ret = aacEncoder_SetParam(aacEncoder, AACENC_TRANSMUX, TT_MP4_ADTS);
    if (AACENC_OK != ret) {
        LOG_E("aacEncoder_SetParam AACENC_TRANSMUX failed, ret: {}", ret);
        return false;
    }

    // 初始化编码器，准备开始编码
    ret = aacEncEncode(
        aacEncoder,
        nullptr,
        nullptr,
        nullptr,
        nullptr
    );
    if (AACENC_OK != ret) {
        LOG_E("aacEncEncode failed, ret: {}", ret);
        return false;
    }

    // 获取编码器信息
    aacEncInfo(aacEncoder, &info);
    LOG_I(
        "audio encoder info => frameLength: {}, channel: {}, maxOutBufBytes: {}",
        info.frameLength,
        info.inputChannels,
        info.maxOutBufBytes
    );

    outBuffer = new uint8_t[info.maxOutBufBytes];

    std::thread task([&]() {
        while (true) {
            std::unique_lock<std::mutex> mainLock(mainMtx);
            mainCV.wait(mainLock, [&] {
                return (!localAudioFrames.empty() &&
                        localAudioFrames.front().size() == localAudioFrames.front().capacity()) ||
                       isMarkFree;
            });
            if (isMarkFree) {
                mainCV.notify_one();
                break;
            }
            auto localAudioFrame = std::move(localAudioFrames.front());
            localAudioFrames.pop();
            mainCV.notify_one();

            std::unique_lock<std::mutex> remoteLock(remoteMtx);
            if (!remoteAudioFrames.empty() &&
                remoteAudioFrames.front().size() == remoteAudioFrames.front().capacity()) {
                auto remoteAudioFrame = std::move(remoteAudioFrames.front());
                remoteAudioFrames.pop();
                remoteLock.unlock();
                mixAudio(remoteAudioFrame, localAudioFrame);
            } else {
                remoteMtx.unlock();
            }

            void *inBufs[] = {localAudioFrame.data()};        // 指向输入音频数据的指针
            INT inBufSizes[] = {(INT)localAudioFrame.size()}; // 输入缓冲区的大小（字节）
            INT inBufElSizes[] = {2};                         // 每个元素的大小（例如 16 位 PCM，大小为 2 字节）
            INT inBufferIdentifiers[] = {IN_AUDIO_DATA};      // 标识为音频输入数据

            AACENC_BufDesc inBufDesc = {
                .numBufs = sizeof(inBufs) / sizeof(void *),
                .bufs = inBufs,
                .bufferIdentifiers = inBufferIdentifiers,
                .bufSizes = inBufSizes,
                .bufElSizes = inBufElSizes,
            };

            // 配置输出缓冲区
            void *outBufs[] = {outBuffer};
            INT outBufSizes[] = {(INT)info.maxOutBufBytes};
            INT outBufElSizes[] = {1};
            INT outBufferIdentifiers[] = {OUT_BITSTREAM_DATA};

            AACENC_BufDesc outBufDesc = {
                .numBufs = sizeof(outBufs) / sizeof(void *),
                .bufs = outBufs,
                .bufferIdentifiers = outBufferIdentifiers,
                .bufSizes = outBufSizes,
                .bufElSizes = outBufElSizes,
            };

            AACENC_InArgs inArgs = {
                .numInSamples = (INT)info.frameLength,
                .numAncBytes = 0,
            };

            AACENC_OutArgs outArgs = {0};

            auto ret = aacEncEncode(
                aacEncoder,
                &inBufDesc,
                &outBufDesc,
                &inArgs,
                &outArgs
            );
            if (AACENC_OK != ret) {
                LOG_E("aacEncEncode failed, ret: {}", ret);
                continue;
            }
            didEncode(outBuffer, outArgs.numOutBytes, pts++ * 1000 * info.frameLength / sampleRate);
        }
        LOG_I("{}", "audio encoder thread free!");
    });
    loopThread = std::move(task);
    return true;
}

bool
AudioEncoder::encode(
    char *frame,
    long frameLen,
    const AudioType type
) {
    auto standFrameLen = info.frameLength * info.inputChannels * 2;
    if (AudioType::local == type) {
        std::unique_lock<std::mutex> lock(mainMtx);
        mainCV.wait(lock, [&] {
            return localAudioFrames.size() < 50;
        });
        pushFrames(
            localAudioFrames.empty(),
            standFrameLen,
            localAudioFrames,
            frame,
            frameLen
        );
        mainCV.notify_one();
    } else {
        std::lock_guard<std::mutex> lock(remoteMtx);
        pushFrames(
            remoteAudioFrames.empty(),
            standFrameLen,
            remoteAudioFrames,
            frame,
            frameLen
        );
    }
    return true;
}

} // namespace MK