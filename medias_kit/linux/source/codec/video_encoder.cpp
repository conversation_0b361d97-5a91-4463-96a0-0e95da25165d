#include "codec/video_encoder.hpp"

#include "utils/logger.hpp"

namespace MK {

void
VideoEncoder::onEncodeCallback(
    void *param,
    const uint8_t *package,
    uint32_t packageLen,
    mw_venc_frame_info_t *packageInfo
) {
    auto encoder = (VideoEncoder *)param;
    encoder->didEncode(package, packageLen, packageInfo);
}

VideoEncoder::VideoEncoder(
    const PixelFormat pixelFormat,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const CallBack didEncode
)
    : didEncode(didEncode),
      pixelFormat(pixelFormat),
      width(width),
      height(height),
      framerate(framerate),
      bitrate(bitrate) {}

VideoEncoder::~VideoEncoder() {
    if (handle) {
        mw_venc_destory(handle);
        handle = nullptr;
    }
    LOG_I("{}", "Memory free!");
}

bool
VideoEncoder::init() {
    if (handle) {
        return true;
    }
    auto gpuNum = mw_venc_get_gpu_num();
    if (gpuNum <= 0) {
        LOG_E("{}", "Not detect any GPU!");
        return false;
    }
    mw_venc_gpu_info_t info;
    int index = 0;
    while (index < gpuNum) {
        if (MW_VENC_STATUS_SUCCESS != mw_venc_get_gpu_info_by_index(index, &info)) {
            LOG_E("{}", "mw_venc_get_gpu_info_by_index failure!");
            return false;
        }
        if (info.platform == MW_VENC_PLATFORM_NVIDIA) {
            break;
        }
        index++;
    }
    auto param = std::make_unique<mw_venc_param_t>();
    if (MW_VENC_STATUS_SUCCESS != mw_venc_get_default_param(param.get())) {
        LOG_E("{}", "mw_venc_get_default_param failure!");
        return false;
    }
    param->level = MW_VENC_LEVEL_5_2;
    param->code_type = MW_VENC_CODE_TYPE_H264;
    param->targetusage = MW_VENC_TARGETUSAGE_BEST_SPEED;
    param->fourcc = pixelFormat == PixelFormat::pf_nv12 ? MW_VENC_FOURCC_NV12
                                                     : MW_VENC_FOURCC_I420;
    param->profile = MW_VENC_PROFILE_H264_BASELINE;
    param->rate_control.mode = MW_VENC_RATECONTROL_CBR;
    param->width = width;
    param->height = height;
    param->fps.num = 10000000;
    param->fps.den = 10000000 / framerate;
    param->rate_control.max_bitrate = bitrate;
    param->rate_control.target_bitrate = bitrate;
    handle = mw_venc_create_by_index(
        index,
        param.get(),
        onEncodeCallback,
        this
    );
    if (!handle) {
        LOG_E("{}", "mw_venc_create_by_index failure!");
        return false;
    }
    return true;
}

bool
VideoEncoder::encode(const uint8_t *const frame, long frameLen) {
    if (!handle) {
        LOG_E("{}", "Encoder is not init!");
        return false;
    }
    auto ret = mw_venc_put_frame_ex(handle, const_cast<uint8_t *>(frame), pts++ * 1000 / framerate);
    if (MW_VENC_STATUS_SUCCESS != ret) {
        LOG_E("mw_venc_put_frame_ex failure! ret = {}", ret);
        return false;
    }
    return true;
}

} // namespace MK
