#include "renderer/audio_renderer.hpp"

#include <chrono>
#include <cstdio>
#include <memory>
#include <mutex>
#include <pulse/error.h>
#include <pulse/simple.h>
#include <thread>
#include <unistd.h>
#include <utility>

#include "utils/logger.hpp"

namespace MK {

AudioRenderer::AudioRenderer(
    const std::shared_ptr<const std::string> renderName,
    const float volume,
    const int sampleRate,
    const bool enableLargeBuffer,
    const bool isMuted
)
    : renderName(renderName),
      sampleRate(sampleRate),
      enableLargeBuffer(enableLargeBuffer),
      volume(volume),
      isMuted(isMuted) {
}

AudioRenderer::~AudioRenderer() {
    isMarkFree = true;
    if (loopThread.joinable()) {
        loopThread.join();
    }
    if (paSimple) {
        int error;
        pa_simple_drain(paSimple, &error);
        pa_simple_free(paSimple);
    }
    LOG_I("{}", "Memory free!");
}

bool
AudioRenderer::init(const std::shared_ptr<const std::string> renderName) {
    if (isMarkFree) {
        return false;
    }
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    if (paSimple && renderName == this->renderName) {
        return true;
    }
    if (renderName && !renderName->empty()) {
        this->renderName = renderName;
        if (paSimple) {
            int error;
            pa_simple_drain(paSimple, &error);
            pa_simple_free(paSimple);
            paSimple = nullptr;
        }
    }

    if (paSimple) {
        return true;
    }

    static const pa_sample_spec ss = {
        .format = PA_SAMPLE_S16LE,
        .rate = (uint32_t)sampleRate,
        .channels = 1,
    };

renderRenew:
    auto realName =
        *this->renderName == "default" ? nullptr : this->renderName->c_str();
    int error;
    paSimple = pa_simple_new(
        nullptr,
        "surgsmart-output",
        PA_STREAM_PLAYBACK,
        realName,
        "playback",
        &ss,
        nullptr,
        nullptr,
        &error
    );
    if (!paSimple) {
        LOG_E("{}", pa_strerror(error));
        if (realName) {
            this->renderName = std::make_shared<std::string>("default");
            goto renderRenew;
        }
        return false;
    }

    if (renderName->empty()) {
        std::thread task([&]() {
            while (true) {
                std::unique_lock<std::mutex> breakLock(breakMtx);
                std::unique_lock<std::mutex> lock(mtx);
                breakLock.unlock();
                if (isMarkFree) {
                    lock.unlock();
                    break;
                }
                auto volume = isMuted ? 0.0f : this->volume;
                if (frames.empty()) {
                    lock.unlock();
                    std::this_thread::sleep_for(
                        std::chrono::milliseconds(10)
                    );
                    continue;
                }
                auto frame = frames.front();
                frames.pop();

                adjustVolume(
                    const_cast<char *>(frame->data),
                    frame->size,
                    volume
                );
                int error;
                auto ret = pa_simple_write(
                    paSimple,
                    frame->data,
                    frame->size,
                    &error
                );
                if (ret < 0) {
                    LOG_E("{}", pa_strerror(error));
                }
                lock.unlock();
            }
            LOG_I("{}", "audio renderer thread free!");
        });
        loopThread = std::move(task);
    }

    return true;
}

void
AudioRenderer::adjustVolume(void *data, int size, float volume) {
    auto buffer = (short *)(data);
    for (int i = 0; i < size / 2; i++) {
        buffer[i] = buffer[i] * volume;
    }
}

void
AudioRenderer::setVolume(float volume) {
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    this->volume = volume;
}

void
AudioRenderer::setMute(bool mute) {
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    isMuted = mute;
}

std::shared_ptr<const std::string>
AudioRenderer::getRenderName() {
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    return renderName;
}

void
AudioRenderer::pushFrame(
    const std::shared_ptr<AudioFrame> frame,
    const unsigned int maxBufferLen
) {
    std::unique_lock<std::mutex> breakLock(breakMtx);
    std::lock_guard<std::mutex> lock(mtx);
    breakLock.unlock();
    if (!enableLargeBuffer && frames.size() > maxBufferLen) {
        while (!frames.empty()) {
            frames.pop();
        }
    }
    frames.push(frame);
}

} // namespace MK
