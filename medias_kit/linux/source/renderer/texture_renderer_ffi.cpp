#include "renderer/texture_renderer_ffi.h"

#include "medias_kit_plugin_private.hpp"
#include "renderer/texture_renderer.hpp"
#include "utils/logger.hpp"

TextureRendererRef
textureRendererCreate(
    const char *pluginId,
    int width,
    int height
) {
    try {
        const auto &plugin = MK::getPlugin(pluginId);
        auto *renderer = new MK::TextureRenderer(plugin, width, height);
        if (!renderer->init()) {
            delete renderer;
            return nullptr;
        }
        return renderer;
    } catch (const std::exception &e) {
        MK::LOG_E("Failed to create texture renderer: {}", e.what());
        return nullptr;
    }
}

void
textureRendererDestroy(TextureRendererRef renderer) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    delete rendererPtr;
}

void
textureRendererRenderFrame(
    TextureRendererRef renderer,
    const uint8_t *const frame,
    long frameLen
) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    rendererPtr->renderFrame(frame, frameLen);
}

int64_t
textureRendererGetTextureId(TextureRendererRef renderer) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    return rendererPtr->getTexture();
}
