#include "renderer/texture_renderer.hpp"

#include <cstring>
#include <epoxy/gl.h>

#include "utils/logger.hpp"

namespace MK {

GLuint
generateEmptyTexture2D() {
    GLuint texture;
    glGenTextures(1, &texture);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
    glBindTexture(GL_TEXTURE_2D, 0);
    return texture;
}

GLuint
generateFrameBufferTexture2D(const GLuint texture) {
    GLuint framebuffer;
    glGenFramebuffers(1, &framebuffer);
    glBindFramebuffer(GL_FRAMEBUFFER, framebuffer);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, texture, 0);
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    return framebuffer;
}

void
commitTexture2DPixels(
    const GLuint texture,
    const GLint innerFormat,
    const GLenum pixelFormat,
    const GLsizei width,
    const GLsizei height,
    const GLenum type,
    const GLvoid *const pixels
) {
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexImage2D(
        GL_TEXTURE_2D,
        0,
        innerFormat,
        width,
        height,
        0,
        pixelFormat,
        type,
        pixels
    );
    glGenerateMipmap(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, 0);
}

struct _TextureGL {
    FlTextureGL parent_instance;
    GLuint textureId;
    GLuint framebufferId;
    guint32 width;
    guint32 height;
    TextureRenderer *renderer;
};

G_DEFINE_TYPE(
    TextureGL,
    texture_gl,
    fl_texture_gl_get_type()
)

gboolean
texture_gl_populate_texture(
    FlTextureGL *texture,
    uint32_t *target,
    uint32_t *name,
    uint32_t *width,
    uint32_t *height,
    GError **error
) {
    TextureGL *self = TEXTURE_GL(texture);
    *target = GL_TEXTURE_2D;
    *name = self->textureId;
    *width = self->width;
    *height = self->height;
    if (self->width > 0 && self->height > 0) {
        // 会导致 Flutter 页面中使用到 openGL 相关的API的组件闪烁
        // glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        std::lock_guard<std::mutex> lock(self->renderer->mtx);
        commitTexture2DPixels(
            self->textureId,
            GL_RGBA,
            GL_RGBA,
            self->width,
            self->height,
            GL_UNSIGNED_BYTE,
            self->renderer->imageBuffer
        );
        return TRUE;
    }
    g_set_error_literal(
        error,
        G_FILE_ERROR,
        G_FILE_ERROR_FAILED,
        "Invalid parameters!"
    );
    return FALSE;
}

void
texture_gl_dispose(GObject *object) {
    TextureGL *self = TEXTURE_GL(object);
    // Free texture & FBO.
    if (self->framebufferId != 0) {
        glDeleteFramebuffers(1, &self->framebufferId);
        self->framebufferId = 0;
    }
    if (self->textureId != 0) {
        glDeleteTextures(1, &self->textureId);
        self->textureId = -1;
    }
    self->width = 0;
    self->height = 0;
    self->renderer = nullptr;
    G_OBJECT_CLASS(texture_gl_parent_class)->dispose(object);
    LOG_I("{}", "Memory free!");
}

void
texture_gl_class_init(TextureGLClass *klass) {
    FL_TEXTURE_GL_CLASS(klass)->populate = texture_gl_populate_texture;
    G_OBJECT_CLASS(klass)->dispose = texture_gl_dispose;
}

void
texture_gl_init(TextureGL *self) {
    self->textureId = -1;
    self->framebufferId = 0;
    self->width = 0;
    self->height = 0;
    self->renderer = nullptr;
}

TextureGL *
texture_gl_new(
    TextureRenderer *renderer,
    guint32 width,
    guint32 height
) {
    TextureGL *self = TEXTURE_GL(
        g_object_new(
            texture_gl_get_type(),
            NULL
        )
    );
    self->renderer = renderer;
    self->width = width;
    self->height = height;
    self->textureId = generateEmptyTexture2D();
    self->framebufferId = generateFrameBufferTexture2D(self->textureId);
    return self;
}

TextureRenderer::TextureRenderer(
    const MediasKitPlugin &plugin,
    const int width,
    const int height
)
    : plugin(plugin),
      width(width),
      height(height),
      imageBuffer(new std::uint8_t[width * height * 4]()) {}

TextureRenderer::~TextureRenderer() {
    fl_texture_registrar_unregister_texture(
        plugin.textureRegistrar,
        FL_TEXTURE(texture)
    );
    textureId = -1;
    if (texture) {
        g_object_unref(texture);
    }
    if (glContext) {
        g_object_unref(glContext);
    }
    delete[] imageBuffer;
    LOG_I("{}", "Memory free!");
}

bool
TextureRenderer::init() {
    auto textureRegistrar = plugin.textureRegistrar;
    auto view = plugin.view;
    auto window = gtk_widget_get_window(GTK_WIDGET(view));
    GError *error = nullptr;
    // WARNING: 不需要手动释放
    glContext = gdk_window_create_gl_context(window, &error);
    if (error) {
        LOG_E("gdk_window_create_gl_context, {}", error->message);
        g_error_free(error);
        return false;
    }
    gdk_gl_context_make_current(glContext);
    glClearColor(0.0, 0.0, 0.0, 1.0);
    gdk_gl_context_realize(glContext, &error);
    if (error) {
        LOG_E("gdk_gl_context_realize, {}", error->message);
        g_error_free(error);
        return false;
    }
    this->texture = texture_gl_new(this, width, height);
    if (!this->texture) {
        LOG_E("{}", "texture_gl_new failure!");
        return false;
    }
    if (!fl_texture_registrar_register_texture(
            textureRegistrar,
            FL_TEXTURE(texture)
        )) {
        LOG_E("{}", "fl_texture_registrar_register_texture failure!");
        return false;
    }
    return true;
}

void
TextureRenderer::renderFrame(
    const uint8_t *const frame,
    const long frameLen
) {
    std::lock_guard<std::mutex> lock(mtx);
    memcpy(imageBuffer, frame, frameLen);
    fl_texture_registrar_mark_texture_frame_available(
        plugin.textureRegistrar,
        FL_TEXTURE(texture)
    );
}

int64_t
TextureRenderer::getTexture() const {
    return (int64_t)texture;
}

} // namespace MK
