#include "rtmp_live_controller/rtmp_live_controller.hpp"
#include "NGIAgoraAudioTrack.h"
#include "NGIAgoraMediaNode.h"
#include "NGIAgoraMediaNodeFactory.h"
#include "codec/audio_encoder.hpp"
#include "utils/logger.hpp"

namespace MK {

RtmpLiveController::RtmpLiveController(
    const std::shared_ptr<const std::string> identifier,
    const std::shared_ptr<const std::string> streamType,
    const std::shared_ptr<const std::string> appId,
    const std::shared_ptr<const std::string> url,
    const std::shared_ptr<const VideoDevice> videoDevice,
    const std::shared_ptr<const std::string> audioCaptureName,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const int sampleRate,
    const int numChannels,
    const bool commitRtcAudio
)
    : localAudioStream(
          new LocalAudioStream(
              this,
              identifier,
              audioCaptureName,
              sampleRate,
              numChannels
          )
      ),
      localVideoStream(
          new LocalVideoStream(
              this,
              identifier,
              videoDevice,
              width,
              height,
              framerate,
              bitrate
          )
      ),
      identifier(identifier),
      streamType(streamType),
      appId(appId),
      url(url) {
    this->commitRtcAudio = commitRtcAudio;
}

RtmpLiveController::~RtmpLiveController() {
    pause = true;
    localVideoStream.reset();
    localAudioStream.reset();
    if (connection) {
        connection->getRtmpLocalUser()->unregisterRtmpUserObserver(this);
        connection->unregisterObserver(this);
        connection->disconnect();
        factory = nullptr;
        connection = nullptr;
    }
    if (service) {
        service->release();
    }
    LOG_I("{}", "Memory free!");
}

bool
RtmpLiveController::init() {
    if (isInit) {
        return isInit;
    }
    // 创建服务
    service = createAgoraService();
    if (!service) {
        LOG_E("{}", "createAgoraService failure!");
        return isInit;
    }

    // 服务配置
    agora::base::AgoraServiceConfiguration serviceConfig;
    const char *home = std::getenv("HOME");
    auto baseDir = std::string(home) + "/Documents/v202310/agora.logs/rtmp_" +
                   *streamType + "/";

    serviceConfig.appId = appId->c_str();
    serviceConfig.logConfig.filePath = baseDir.c_str();
    serviceConfig.enableAudioProcessor = true;
    serviceConfig.enableAudioDevice = false;
    serviceConfig.domainLimit = true;

    // 初始化服务
    if (agora::ERR_OK != service->initialize(serviceConfig)) {
        LOG_E("{}", "service initialize failure!");
        return isInit;
    }

    // 连接配置
    agora::rtc::RtmpConnectionConfiguration connectConfig;
    connectConfig.videoConfig.width = localVideoStream->capture->width;
    connectConfig.videoConfig.height = localVideoStream->capture->height;
    connectConfig.videoConfig.framerate = localVideoStream->capture->framerate;
    connectConfig.videoConfig.bitrate = localVideoStream->bitrate;
    connectConfig.videoConfig.encoderHwSwMode = 1;
    connectConfig.videoConfig.encoderBitrateControlMode = 1;

    connectConfig.audioConfig.sampleRateHz =
        localAudioStream->capture->sampleRate;
    connectConfig.audioConfig.numberOfChannels =
        localAudioStream->capture->numChannels;

    // 初始化连接
    connection = service->createRtmpConnection(connectConfig);
    if (!connection) {
        LOG_E("{}", "service createRtmpConnection failure!");
        return isInit;
    }

    connection->getRtmpLocalUser()->setVideoEnabled(true);

    // 注册连接状态观察者
    auto ret = connection->registerObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 注册用户状态观察者
    ret = connection->getRtmpLocalUser()->registerRtmpUserObserver(this);
    if (agora::ERR_OK != ret) {
        LOG_E("connection registerRtmpUserObserver failure! errcode: {}", ret);
        return isInit;
    }

    // 开始连接
    ret = connection->connect(url->c_str());
    if (agora::ERR_OK != ret) {
        LOG_E("connection connect failure! errcode: {}", ret);
        return isInit;
    }

    // 创建媒体节点工厂
    factory = service->createMediaNodeFactory();
    if (!factory) {
        LOG_E("{}", "service createMediaNodeFactory failure!");
        return isInit;
    }

    // 初始化本地主音频流
    if (!localAudioStream->init()) {
        LOG_E("{}", "mainLocalAudioStream init failure!");
        return isInit;
    }

    // 初始化本地主视频流
    if (!localVideoStream->init()) {
        LOG_E("{}", "mainLocalVideoStream init failure!");
        return isInit;
    }

    pause = false;
    return isInit = true;
}

void
RtmpLiveController::start() {
    pause = false;
    LOG_I("{}", "start live");
}

void
RtmpLiveController::stop() {
    pause = true;
    LOG_I("{}", "stop live");
}

void
RtmpLiveController::speak(bool enable) {
    enableSpeak = enable;
    LOG_I("speak enabled {}", enable);
}

bool
RtmpLiveController::updateAudioCapture(
    const std::shared_ptr<const std::string> audioCaptureName
) {
    if (!localAudioStream->capture->init(audioCaptureName)) {
        LOG_E("{}", "audioCapture init failure!");
        return false;
    }
    return true;
}

bool
RtmpLiveController::isCommitRtcAudio() {
    return commitRtcAudio;
}

#pragma mark-- ILocalVideoStreamController

agora::agora_refptr<agora::rtc::IVideoFrameSender>
RtmpLiveController::createVideoFrameSender() const {
    return factory->createVideoFrameSender();
}

agora::agora_refptr<agora::rtc::ILocalVideoTrack>
RtmpLiveController::createCustomVideoTrack(
    agora::agora_refptr<agora::rtc::IVideoFrameSender> sender
) const {
    return service->createCustomVideoTrack(sender);
}

int
RtmpLiveController::publishVideo(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) const {
    return connection->getRtmpLocalUser()->publishVideo(videoTrack);
}

int
RtmpLiveController::unpublishVideo(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) const {
    return connection->getRtmpLocalUser()->unpublishVideo(videoTrack);
}

bool
RtmpLiveController::canCommitVideo() const {
    return connected && !pause;
}

#pragma mark-- ILocalAudioStreamController

agora::agora_refptr<agora::rtc::IAudioPcmDataSender>
RtmpLiveController::createAudioPcmDataSender() const {
    return factory->createAudioPcmDataSender();
}

agora::agora_refptr<agora::rtc::ILocalAudioTrack>
RtmpLiveController::createCustomAudioTrack(
    agora::agora_refptr<agora::rtc::IAudioPcmDataSender> sender
) const {
    return service->createCustomAudioTrack(sender);
}

int
RtmpLiveController::publishAudio(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) const {
    return connection->getRtmpLocalUser()->publishAudio(audioTrack);
}

int
RtmpLiveController::unpublishAudio(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) const {
    return connection->getRtmpLocalUser()->unpublishAudio(audioTrack);
}

bool
RtmpLiveController::canCommitAudio() const {
    return connected && enableSpeak && !pause;
}

int
RtmpLiveController::pushRtcRemoteAudio(char *frame, int frameLen) {

    std::lock_guard<std::mutex> lock(remoteMtx);
    if (rtcAudioFrames.size() >= 50) {
        rtcAudioFrames.pop();
    }

    auto sharedVec = std::make_shared<std::vector<char>>(frame, frame + frameLen);
    rtcAudioFrames.push(sharedVec);

    mainCV.notify_one();
    return 0;
}

const std::shared_ptr<const std::vector<char>>
RtmpLiveController::audioHandler(const char *frame, std::size_t frameLen) {
    auto frames = std::make_shared<std::vector<char>>(frame, frame + frameLen);

    if (!rtcAudioFrames.empty()) {
        std::unique_lock<std::mutex> lock(remoteMtx);
        mainCV.wait(lock, [this]() {
            return !rtcAudioFrames.empty();
        });
        auto remoteFrames = rtcAudioFrames.front();
        rtcAudioFrames.pop();

        if (remoteFrames && !remoteFrames->empty()) {
            if (frameLen <= 0) {
                // memcpy((char *)frame, remoteFrames->data(), remoteFrames->size());
                return remoteFrames;
            } else {
                AudioEncoder::mixAudio(*remoteFrames, *frames);
            }
        }
    }

    return frames;
}

#pragma mark-- IRtmpConnectionObserver

void
RtmpLiveController::onReconnecting(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = false;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLiveController::onConnected(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = true;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLiveController::onReconnected(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = true;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLiveController::onDisconnected(
    const agora::rtc::RtmpConnectionInfo &connectionInfo
) {
    connected = false;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLiveController::onConnectionFailure(
    const agora::rtc::RtmpConnectionInfo &connectionInfo,
    agora::rtc::RTMP_CONNECTION_ERROR errCode
) {
    connected = false;
    LOG_I("state: {}", connectionInfo.state);
}

void
RtmpLiveController::onTransferStatistics(
    uint64_t video_width,
    uint64_t video_height,
    uint64_t video_bitrate,
    uint64_t audio_bitrate,
    uint64_t video_frame_rate,
    uint64_t push_video_frame_cnt,
    uint64_t pop_video_frame_cnt
) {}

#pragma mark-- IRtmpLocalUserObserver

void
RtmpLiveController::onAudioTrackPublishSuccess(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack
) {
    LOG_I("{}", "publish audio success!");
}

void
RtmpLiveController::onAudioTrackPublicationFailure(
    agora::agora_refptr<agora::rtc::ILocalAudioTrack> audioTrack,
    agora::rtc::PublishAudioError error
) {
    LOG_E("{}", "publish audio failure!");
}

void
RtmpLiveController::onVideoTrackPublishSuccess(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack
) {
    LOG_I("{}", "publish video success!");
}

void
RtmpLiveController::onVideoTrackPublicationFailure(
    agora::agora_refptr<agora::rtc::ILocalVideoTrack> videoTrack,
    agora::rtc::PublishVideoError error
) {
    LOG_E("{}", "publish video failure!");
}

} // namespace MK
