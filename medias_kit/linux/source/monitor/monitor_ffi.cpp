#include "monitor/monitor_ffi.h"

#include "monitor/monitor.hpp"
#include "renderer/texture_renderer.hpp"
#include "stream&capture/video_capture.hpp"
#include "types/types_ffi.h"
#include "utils/logger.hpp"

MonitorRef
monitorCreate() {
    try {
        auto *monitor = new MK::Monitor();
        return monitor;
    } catch (const std::exception &e) {
        MK::LOG_E("Failed to create monitor: {}", e.what());
        return nullptr;
    }
}

void
monitorDestroy(MonitorRef monitor) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    delete monitorPtr;
}

void
monitorBind(
    MonitorRef monitor,
    void *capture,
    void *renderer,
    bool autoStart
) {
    assert(monitor && capture && renderer);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    auto *videoCapture = static_cast<MK::VideoCapture *>(capture);
    auto *textureRenderer = static_cast<MK::TextureRenderer *>(renderer);
    monitorPtr->bind(videoCapture, textureRenderer, autoStart);
}

void
monitorStart(MonitorRef monitor) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    monitorPtr->start();
}

void
monitorStop(MonitorRef monitor) {
    assert(monitor);
    auto *monitorPtr = static_cast<MK::Monitor *>(monitor);
    monitorPtr->stop();
}
