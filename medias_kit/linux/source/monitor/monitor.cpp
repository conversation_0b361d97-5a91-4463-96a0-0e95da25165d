#include "monitor/monitor.hpp"

#include <cassert>
#include <epoxy/gl.h>

#include "utils/logger.hpp"

namespace MK {

Monitor::Monitor() {}

Monitor::~Monitor() {
    stop();
    LOG_I("{}", "Memory free!");
}

void
Monitor::bind(
    VideoCapture *const capture,
    TextureRenderer *const renderer,
    const bool autoStart
) {
    assert(capture && renderer);
    stop();
    if (this->capture) {
        this->capture->bindFrameCallBack(nullptr);
    }
    capture->bindFrameCallBack(
        [this](
            const PixelFormat pixelFormat,
            const uint8_t *const frame,
            const long frameLen
        ) {
            assert(pixelFormat == PixelFormat::pf_rgba);
            if (!this->pause) {
                this->renderer->renderFrame(frame, frameLen);
            }
        }
    );
    this->capture = capture;
    this->renderer = renderer;
    if (autoStart) {
        start();
    }
}

void
Monitor::start() {
    pause = false;
}

void
Monitor::stop() {
    pause = true;
}

} // namespace MK
