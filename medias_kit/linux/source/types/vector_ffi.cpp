
#include "types/vector_ffi.h"

#include <vector>

#include "utils/logger.hpp"

VectorRef
vectorCreate() {
    return new std::vector<void *>();
}

void
vectorDestroy(VectorRef vector, bool freeElements) {
    assert(vector);
    auto *vectorPtr = static_cast<std::vector<void *> *>(vector);
    if (freeElements) {
        for (auto element : *vectorPtr) {
            free(element);
        }
    }
    delete vectorPtr;
}

int
vectorSize(VectorRef vector) {
    assert(vector);
    return static_cast<std::vector<void *> *>(vector)->size();
}

void *
vectorElementAt(VectorRef vector, int index) {
    assert(vector);
    return static_cast<std::vector<void *> *>(vector)->at(index);
}
