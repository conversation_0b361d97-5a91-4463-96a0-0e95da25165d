# 仅对非 ffi-native 生效
name: VectorBindings
# 仅对非 ffi-native 生效
description: FFI bindings for VectorBindings
# 基于 yaml 文件当前路径
output: ../lib/_ffigen/vector_bindings.dart
ffi-native:
  # 作用指定动态库加载路径
  asset-id: package:medias_kit/_ffigen/vector_bindings.dart
preamble: |
  // 功能: 向量FFI函数绑定
  //
  // 该文件定义了向量操作的FFI绑定，用于在Dart和C++之间传递向量数据
headers:
  entry-points:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/types/vector_ffi.h
  include-directives:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/types/vector_ffi.h
compiler-opts:
  # 基于 `dart run ffigen --config <yaml-file-path>` 命令执行路径
  - "-Ilinux/include/private"
  - "-I{FLUTTER_ROOT}/bin/cache/dart-sdk/include"
