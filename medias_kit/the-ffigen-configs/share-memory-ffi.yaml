# 仅对非 ffi-native 生效
name: SharedMemoryBindings
# 仅对非 ffi-native 生效
description: FFI bindings for SharedMemoryBindings
# 基于 yaml 文件当前路径
output: ../lib/_ffigen/shared_memory_bindings.dart
ffi-native:
  # 作用指定动态库加载路径
  asset-id: package:medias_kit/_ffigen/shared_memory_bindings.dart
preamble: |
  // 功能: 共享内存FFI函数绑定
headers:
  entry-points:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/shared_memory/shared_memory_ffi.h
  include-directives:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/shared_memory/shared_memory_ffi.h
compiler-opts:
  # 基于 `dart run ffigen --config <yaml-file-path>` 命令执行路径
  - "-Ilinux/include/private"
