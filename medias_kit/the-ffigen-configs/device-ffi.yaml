# 仅对非 ffi-native 生效
name: DeviceBindings
# 仅对非 ffi-native 生效
description: FFI bindings for DeviceBindings
# 基于 yaml 文件当前路径
output: ../lib/_ffigen/device_bindings.dart
ffi-native:
  # 作用指定动态库加载路径
  asset-id: package:medias_kit/_ffigen/device_bindings.dart
preamble: |
  // 功能: 设备FFI函数绑定
headers:
  entry-points:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/device/device_ffi.h
  include-directives:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/device/device_ffi.h
compiler-opts:
  # 基于 `dart run ffigen --config <yaml-file-path>` 命令执行路径
  - "-Ilinux/include/private"
  - "-I{FLUTTER_ROOT}/bin/cache/dart-sdk/include"
