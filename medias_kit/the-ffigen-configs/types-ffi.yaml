# 仅对非 ffi-native 生效
name: TypesBindings
# 仅对非 ffi-native 生效
description: FFI bindings for Types
# 基于 yaml 文件当前路径
output: ../lib/_ffigen/types_bindings.dart
ffi-native:
  # 作用指定动态库加载路径
  asset-id: package:medias_kit/_ffigen/types_bindings.dart
preamble: |
  // 功能: 类型FFI映射绑定
  //
  // 该文件定义了 dart 与 C 交互的基本类型和枚举
headers:
  entry-points:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/types/types_ffi.h
  include-directives:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/types/types_ffi.h
compiler-opts:
  # 基于 `dart run ffigen --config <yaml-file-path>` 命令执行路径
  - "-Ilinux/include/private"
