#version 460 core

precision highp float;

#include <flutter/runtime_effect.glsl>

uniform float uRadius;
uniform vec2 uSize;
uniform sampler2D uTexture;

out vec4 fragColor;

void main() {
    vec2 Radius = uRadius / uSize;
    vec2 uv = FlutterFragCoord() / uSize;
    vec4 Color = texture(uTexture, uv);
    for(float d = 0.0; d < 6.28; d += 6.28 / 16.0) {
		for(float i= 1.0 / 3.0; i <= 1.0; i += 1.0 / 3.0) {
			Color += texture(uTexture, uv+vec2(cos(d),sin(d))*Radius*i);		
        }
    }
    Color /= 3.0 * 16.0 - 15.0;
    fragColor =  Color;
}