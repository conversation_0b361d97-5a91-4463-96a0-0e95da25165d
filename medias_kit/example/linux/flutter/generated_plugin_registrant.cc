//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <app_foundation/app_foundation_plugin.h>
#include <desktop_multi_window/desktop_multi_window_plugin.h>
#include <flutter_libserialport/flutter_libserialport_plugin.h>
#include <medias_kit/medias_kit_plugin.h>
#include <url_launcher_linux/url_launcher_plugin.h>

void fl_register_plugins(FlPluginRegistry* registry) {
  g_autoptr(FlPluginRegistrar) app_foundation_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "AppFoundationPlugin");
  app_foundation_plugin_register_with_registrar(app_foundation_registrar);
  g_autoptr(FlPluginRegistrar) desktop_multi_window_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "DesktopMultiWindowPlugin");
  desktop_multi_window_plugin_register_with_registrar(desktop_multi_window_registrar);
  g_autoptr(FlPluginRegistrar) flutter_libserialport_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "FlutterLibserialportPlugin");
  flutter_libserialport_plugin_register_with_registrar(flutter_libserialport_registrar);
  g_autoptr(FlPluginRegistrar) medias_kit_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "MediasKitPlugin");
  medias_kit_plugin_register_with_registrar(medias_kit_registrar);
  g_autoptr(FlPluginRegistrar) url_launcher_linux_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "UrlLauncherPlugin");
  url_launcher_plugin_register_with_registrar(url_launcher_linux_registrar);
}
