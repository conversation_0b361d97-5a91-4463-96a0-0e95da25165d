#!/usr/bin/env bash

export FLUTTER_ENGINE_LOGGING=true

export DISPLAY=:0

XF_ASSETS=$(cd ../ && pwd)/assets
export XF_ASSETS

LD_LIBRARY_PATH=$(cd ../ && pwd)/linux/dependencies/xfyun_sdk/libs
export LD_LIBRARY_PATH

if [[ "${1}" == "--debug" ]]; then
    flutter build linux --debug || exit 0
    lldb ./build/linux/x64/debug/bundle/medias_kit_example
elif [[ "${1}" == "--release" ]]; then
    flutter run -d linux --release
elif [[ "${1}" == "--verbose" ]]; then
    flutter run -d linux --verbose
else
    flutter run -d linux
fi
