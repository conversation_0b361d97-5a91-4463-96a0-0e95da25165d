#!/usr/bin/env bash

# flutter clean

# flutter build linux --release

cd build/linux/x64/release/bundle/ || exit

mkdir -p ./DEBIAN

cat > DEBIAN/control << eof
Package: medias-kit-xample
Version: 1.0.0
Architecture: amd64
Maintainer: <EMAIL>
Description: 媒体库例程
Depends: libpci3 (>= 1:3.7.0-5ubuntu2), libasound2 (>= 1.2.4-1.1ubuntu3), libudev1 (>= 249.11-0ubuntu3.12), libv4l-0 (>= 1.22.1-2build1), libepoxy0 (>= 1.5.10-1), libswscale5 (>= 7:4.4.2-0ubuntu0.22.04.1), libdl2 (>= ), zlib1g (>= 1:1.2.11.dfsg-2ubuntu9.2), libresolv1 (>= ), libva-drm2 (>= 2.14.0-1), libva2 (>= 2.14.0-1), libfmt7 (>= 7.1.3+ds1-5), libspdlog1 (>= 1:1.9.2+ds-0.2), libsdl2-2.0-0 (>= 2.0.20+dfsg-2ubuntu1.22.04.1)
eof

# dpkg-deb --build ./


# zip -q -r ../medias-kit-example.pkg ./

# cd ..

# path=$(pwd)/medias-kit-example.pkg

printf "  \033[32mArchive Success!\033[0m\n"

# printf "  \033[32m%s\033[0m\n" "$path"
