import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:medias_kit_example/src/routes/go_paths.dart';

/// 请先移步至 ./src/routes/go_paths.dart 中, 定义路由信息
import './src/routes/go_routes.dart';

/// APP 页面路由器
final router = GoRouter(
  observers: [AppRouteObserver.share],
  navigatorKey: app.navigatorKey,
  routes: routes,
  initialLocation: GoPaths.home,
);

@AppDetector()
void main(List<String> args) {
  if (args.firstOrNull == 'multi_window') {
    runApp(
      AppBuilder(
        builder: (context, env) {
          return MaterialApp.router(
            scaffoldMessengerKey: app.messengerKey,
            routerConfig: router,
            theme: ThemeData(useMaterial3: true),
          );
        },
        onWillInit: () async {
          await MediasKit.ensureInitialized();
        },
        onDidInit: (env) {
          app.setLog(Level.all);
          app.logI("App did init");
        },
      ),
    );
  } else {
    runApp(
      AppBuilder(
        builder: (context, env) {
          return MaterialApp.router(
            scaffoldMessengerKey: app.messengerKey,
            routerConfig: router,
            theme: ThemeData(useMaterial3: true),
          );
        },
        onWillInit: () async {
          await MediasKit.ensureInitialized();
        },
        onDidInit: (env) {
          app.setLog(Level.all);
          app.logI("App did init");
        },
      ),
    );
  }
}
