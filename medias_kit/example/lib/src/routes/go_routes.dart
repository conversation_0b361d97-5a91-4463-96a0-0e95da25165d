// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// RouterGenerator
// **************************************************************************

import 'package:go_router/go_router.dart';

import 'go_paths.dart';

// 🏷️ module =====> home
import '../modules/home/<USER>/home.controller.dart';
import '../modules/home/<USER>/home.view.dart';

// 🏷️ module =====> next
import '../modules/next/next/next.controller.dart';
import '../modules/next/next/next.view.dart';

// 🏷️ module =====> multi
import '../modules/multi/multi/multi.controller.dart';
import '../modules/multi/multi/multi.view.dart';

// 🏷️ module =====> test
import '../modules/test/test/test.controller.dart';
import '../modules/test/test/test.view.dart';

/// APP 路由列表
final List<GoRoute> routes = [
  // 🏷️ module =====> home
  GoRoute(
    name: GoPaths.home,
    path: GoPaths.home,
    builder: (context, state) => HomeView(
      key: state.pageKey,
      binding: (key) => HomeController(key, state),
    ),
  ),

  // 🏷️ module =====> next
  GoRoute(
    name: GoPaths.next,
    path: GoPaths.next,
    builder: (context, state) => NextView(
      key: state.pageKey,
      binding: (key) => NextController(key, state),
    ),
  ),

  // 🏷️ module =====> multi
  GoRoute(
    name: GoPaths.multi,
    path: GoPaths.multi,
    builder: (context, state) => MultiView(
      key: state.pageKey,
      binding: (key) => MultiController(key, state),
    ),
  ),

  // 🏷️ module =====> test
  GoRoute(
    name: GoPaths.test,
    path: GoPaths.test,
    builder: (context, state) => TestView(
      key: state.pageKey,
      binding: (key) => TestController(key, state),
    ),
  ),
];
