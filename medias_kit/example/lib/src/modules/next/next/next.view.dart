import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:screenshot/screenshot.dart';

import 'next.controller.dart';

/// 所属模块: next
///
/// 采集
class NextView extends AppView<NextController> {
  const NextView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.videoDevice.name),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Center(
      child: Screenshot(
        controller: controller.screenshotController,
        child: MonitorView(monitorController: controller.monitorController),
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
