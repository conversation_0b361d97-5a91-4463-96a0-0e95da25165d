import 'package:app_foundation/app_foundation.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:screenshot/screenshot.dart';

/// 所属模块: next
///
/// 采集
class NextController extends AppController with StateMixin<ApiModel> {
  NextController(super.key, super.routerState);

  late final videoDevice = routerState?.extra as VideoDevice;

  final screenshotController = ScreenshotController();
  late final monitorController = MonitorController(
    videoDevice: videoDevice,
    width: 1920,
    height: 1080,
    framerate: 30,
  );
  late final recordController = RecordController(
    spaceName: "999999",
    videoDevice: videoDevice,
    videoBitrate: 4096,
    onMerged: (videoInfo) {
      app.logD(">>>>>>>>>>>>>>>>>merged");
      app.logD(videoInfo.toString());
    },
  );
  late final rtcLiveController = RtcLiveController(
    streamType: StreamType.main,
    videoDevice: videoDevice,
    appId: "44f6c4f99b414bbaa2302b03ea08c0b1",
    token:
        "007eJxTYNC8FHYqLULT9p0Fo+zUXrE3ZvP5gqa3JTze8z7/qtmabTcVGExM0sySTdIsLZNMDE2SkhITjYwNjJIMjFMTDSySDZIMFynUpR9YwcDQ8SWDhZGBkYEFiEGACUwyg0kWMMnBkBuQnW5aHmDBzGBkZA4AN3QhIQ==",
    channelId: "mPkg5wP8",
    userId: "227",
    recordControllerIdentifier: recordController.identifier,
    onUserJoined: (userId) {
      app.logD(">>>>>>>>>>>>>>>>>user joined: $userId");
    },
    onUserLeft: (userId, reason) {
      app.logD(">>>>>>>>>>>>>>>>>user left: $userId, reason: $reason");
    },
    onTransportStats: (rtcStats) {
      // app.logD(
      //   ">>>>>>>>>>>>>>>>>video bitrate: ${rtcStats.sendVideoBitrate}kbps, sendLossRate: ${rtcStats.sendLossRate}",
      // );
    },
  );
  late final rtmpLiveController = RtmpLiveController(
    videoDevice: videoDevice,
    streamType: StreamType.main,
    appId: "44f6c4f99b414bbaa2302b03ea08c0b1",
    url: 'rtmp://push.surgsmart.com/live/10007',
  );

  @override
  void onInit() {
    monitorController.init().then(
      (value) {
        // monitorController.shader = ShaderAssets.gaussianBlur;
      },
    );

    // recordController.init().then((value) {
    //   recordController.recordAudio(true);
    //   // recordController.stop();
    // });

    // rtcLiveController.init().then(
    //   (value) {
    //     rtcLiveController.speak(true);
    //   },
    // );

    // rtmpLiveController.init();
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    monitorController.dispose();
    recordController.dispose();
    rtcLiveController.dispose();
    rtmpLiveController.dispose();
  }
}
