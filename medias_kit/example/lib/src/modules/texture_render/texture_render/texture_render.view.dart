import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';

import 'texture_render.controller.dart';

/// 所属模块: texture_render
///
/// 纹理渲染测试
class TextureRenderView extends AppView<TextureRenderController> {
  const TextureRenderView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: const Text('TextureRenderView'),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return const Center(
      child: Text('This is TextureRenderView, replace me!'),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
