import 'package:app_foundation/app_foundation.dart';
import 'package:medias_kit/medias_kit.dart';

/// 所属模块: home
///
/// 首页
class HomeController extends AppController
    with StateMixin<ApiModel>, VoiceHandler {
  HomeController(super.key, super.routerState);

  String platformVersion = HostDevice.share.getPlatform();
  final units = ['PB', 'TB', 'GB', 'MB', 'KB', 'B'];

  @override
  void onInit() {
    app.logD(platformVersion);
    final spaceInfo =
        HostDevice.share.buildingWithSpaceInfo(building: (spaceInfo) {
      return (
        capacity: spaceInfo.capacity,
        free: spaceInfo.free,
        available: spaceInfo.available
      );
    });
    app.logD(
        "SpaceInfo | capacity: ${spaceInfo.capacity.bitIndent(bitStep: 10, units: units)}, free: ${spaceInfo.free.bitIndent(bitStep: 10, units: units)}, available: ${spaceInfo.available.bitIndent(bitStep: 10, units: units)}");
    final videoCaptures = HostDevice.share.getVideoCaptures();
    app.logD(videoCaptures);
    app.logE(routerState?.extra);
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {}

  @override
  VoiceTask? hitTest(String command) {
    return VoiceTask(
      (command) async {
        if (command.contains("再见") || command.contains("拜拜")) {
          return const VoiceTaskResult(
            state: VoiceTaskState.done,
            remindWords: "再见",
          );
        }
        if (command.contains("邀请")) {
          return const VoiceTaskResult(
            state: VoiceTaskState.next,
            remindWords: "为您找到5位神医 请问要邀请第几位",
          );
        }
        return const VoiceTaskResult(remindWords: "不支持的命令");
      },
    ).nextTask(
      (command) async {
        if (command.contains("1")) {
          return const VoiceTaskResult(
            state: VoiceTaskState.done,
            remindWords: "已为您邀请第1位神医",
          );
        }
        return const VoiceTaskResult();
      },
    );
  }
}
