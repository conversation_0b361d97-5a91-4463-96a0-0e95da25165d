# modules

业务模块文档, 由 APPFoundation 生成，请勿手动修改

请在 [go_paths](../routes/go_paths.dart) 文件中配置路由信息并完善相关注解

## home 模块

| 名称 | 视图 | 控制器 | 文档 | 描述 |
| :-- | :-- | :-- | :-- | :-- |
| [home](./home/<USER>/home/<USER>/home.view.dart) | [HomeController](./home/<USER>/home/<USER>/README.md) | 首页 |

## next 模块

| 名称 | 视图 | 控制器 | 文档 | 描述 |
| :-- | :-- | :-- | :-- | :-- |
| [next](./next/next) | [NextView](./next/next/next.view.dart) | [NextController](./next/next.controller.dart) | [README](./next/next/README.md) | 采集 |

## multi 模块

| 名称 | 视图 | 控制器 | 文档 | 描述 |
| :-- | :-- | :-- | :-- | :-- |
| [multi](./multi/multi) | [MultiView](./multi/multi/multi.view.dart) | [MultiController](./multi/multi.controller.dart) | [README](./multi/multi/README.md) | 多路采集 |

## test 模块

| 名称 | 视图 | 控制器 | 文档 | 描述 |
| :-- | :-- | :-- | :-- | :-- |
| [test](./test/test) | [TestView](./test/test/test.view.dart) | [TestController](./test/test.controller.dart) | [README](./test/test/README.md) | 音频设备测试 |

[返回上级](../README.md)
